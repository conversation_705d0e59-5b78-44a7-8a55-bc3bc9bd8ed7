{"name": "app", "type": "module", "private": true, "scripts": {"prepare": "nuxt prepare", "dev": "NODE_OPTIONS=\"--require=../patches/patch-spawn-esbuild.cjs\" nuxt dev --no-fork", "build": "nuxt build", "generate": "nuxt generate", "typecheck": "nuxt typecheck", "clean": "rimraf .nuxt .output node_modules"}, "dependencies": {"@cssninja/nuxt-toaster": "^0.5.0", "@headlessui/vue": "^1.7.23", "firebase": "^12.3.0", "firebase-admin": "^13.5.0"}, "devDependencies": {"@iconify-json/akar-icons": "^1.2.2", "@iconify-json/bi": "^1.2.2", "@iconify-json/cib": "^1.2.2", "@iconify-json/cryptocurrency": "^1.2.2", "@iconify-json/devicon": "^1.2.23", "@iconify-json/fa-brands": "^1.2.1", "@iconify-json/fa6-brands": "^1.2.5", "@iconify-json/feather": "^1.2.1", "@iconify-json/file-icons": "^1.2.1", "@iconify-json/flagpack": "^1.2.2", "@iconify-json/gg": "^1.2.2", "@iconify-json/guidance": "^1.2.2", "@iconify-json/ic": "^1.2.2", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify-json/ion": "^1.2.2", "@iconify-json/logos": "^1.2.4", "@iconify-json/lucide": "^1.2.34", "@iconify-json/mdi": "^1.2.2", "@iconify-json/mdi-light": "^1.2.2", "@iconify-json/mingcute": "^1.2.3", "@iconify-json/nonicons": "^1.2.2", "@iconify-json/pajamas": "^1.2.4", "@iconify-json/ph": "^1.2.2", "@iconify-json/ri": "^1.2.5", "@iconify-json/simple-icons": "^1.2.30", "@iconify-json/solar": "^1.2.2", "@iconify-json/streamline": "^1.2.2", "@iconify-json/system-uicons": "^1.2.2", "@iconify-json/teenyicons": "^1.2.2", "@iconify-json/token-branded": "^1.2.16", "@iconify-json/uiw": "^1.2.1", "@iconify-json/unjs": "^1.2.0", "@iconify-json/vaadin": "^1.2.1", "@mapbox/mapbox-gl-geocoder": "5.1.2", "@nuxt/fonts": "^0.11.1", "@nuxt/image": "1.11.0", "@nuxt/kit": "^4.1.2", "@nuxtjs/i18n": "^10.1.0", "@shuriken-ui/nuxt": "4.0.0-beta.4", "@tailwindcss/typography": "^0.5.16", "@types/geojson": "^7946.0.15", "@types/mapbox-gl": "3.4.1", "@types/mapbox__mapbox-gl-geocoder": "^5.0.0", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.0.0", "@vueuse/nuxt": "^13.0.0", "@zxcvbn-ts/core": "^3.0.4", "@zxcvbn-ts/language-common": "^3.0.4", "@zxcvbn-ts/language-en": "^3.0.2", "@zxcvbn-ts/language-fr": "^3.0.2", "apexcharts": "5.3.5", "country-codes-list": "^2.0.0", "date-fns": "4.1.0", "debug": "^4.3.7", "geojson": "^0.5.0", "imask": "7.6.1", "klona": "^2.0.6", "libphonenumber-js": "^1.12.6", "lightningcss": "^1.29.2", "lightweight-charts": "^5.0.5", "magic-string": "0.30.19", "mapbox-gl": "3.15.0", "minisearch": "^7.1.2", "nuxt": "4.1.2", "nuxt-component-meta": "0.14.0", "ohash": "^2.0.11", "pathe": "^2.0.3", "reka-ui": "^2.2.0", "scule": "^1.3.0", "sharp": "0.34.4", "shiki": "3.13.0", "slugify": "^1.6.6", "tailwindcss": "^4.1.3", "turbois-db": "file:../db/dataconnect-generated/javascript-sdk", "typescript": "5.9.2", "v-calendar": "2.4.2", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-component-meta": "3.0.7", "vue-router": "^4.5.0", "vue3-apexcharts": "1.8.0", "vue3-smooth-dnd": "0.0.6", "zod": "^4.1.11"}}