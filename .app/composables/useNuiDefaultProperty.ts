// Composable to provide a default property value for Nui components
// Usage: useNuiDefaultProperty(props, componentName, propName)
import { computed } from 'vue'

/**
 * Returns the value of a prop, falling back to a global default if not provided.
 *
 * @param props - The props object from the component
 * @param componentName - The name of the component (e.g., 'BaseInput')
 * @param propName - The name of the prop to resolve
 * @returns A computed ref with the resolved value
 */
export function useNuiDefaultProperty<T = any>(
  props: Record<string, any>,
  componentName: string,
  propName: string
) {
  // 1. Use the prop if provided
  if (props[propName] !== undefined) {
    return computed(() => props[propName])
  }

  // 2. Try to get a global default (e.g., from provide/inject or a config)
  // For now, just return undefined as a fallback
  // You can extend this to use Nuxt provide/inject or a config plugin
  return computed(() => undefined)
}
