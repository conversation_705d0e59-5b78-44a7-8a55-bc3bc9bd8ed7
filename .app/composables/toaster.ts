import type { Toaster } from '#components'
import type {
  DefaultP<PERSON>,
  NinjaToasterBaseProps,
} from '@cssninja/nuxt-toaster'

// This type infer the props of Toaster component
type ToasterProps = Omit<
  InstanceType<typeof Toaster>['$props'],
  keyof DefaultProps
>

export function useToaster() {
  const { $nt } = useNuxtApp()

  /**
   * Display a Toaster component
   */
  function show(props: ToasterProps, options?: NinjaToasterBaseProps) {
    return $nt.showComponent('Toaster', {
      props,
      options: {
        ...options,
        position: 'top-right', // Set default position
        duration: 5000, // Set default duration (5 seconds)
      },
    })
  }

  return {
    show,
    clear: $nt.clear,
    clearAll: $nt.clearAll,
  }
}

export function useToasterState() {
  const { $nt } = useNuxtApp()
  return $nt.state
}

export function useToasterProgress() {
  const { $nt } = useNuxtApp()
  return $nt.progress
}
