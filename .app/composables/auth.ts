import {
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  signOut,
} from 'firebase/auth'

export function useFirebaseAuth() {
  const { auth, dataConnect } = useFirebase()

  const registerFirebaseUser = async (email: string, password: string) => {
    return await createUserWithEmailAndPassword(auth, email, password)
      .then(async (userCredential: any) => {
        const idToken = userCredential._tokenResponse.idToken
        // dataConnect.setIdToken(idToken)
        return {
          type: 'register-success',
          title: 'Register success',
          message: 'You are logged in 🔥🔥',
          code: 200,
          data: userCredential.user,
          tokenResponse: userCredential._tokenResponse,
        }
      })
      .catch((error: any) => {
        return {
          type: 'register-error',
          title: 'Register error',
          message: error.message,
          code: error.code,
          data: error,
        }
      })
  }

  const loginFirebaseUser = async (email: string, password: string) => {
    return await signInWithEmailAndPassword(auth, email, password)
      .then(async (userCredential: any) => {
        const idToken = userCredential._tokenResponse.idToken
        // dataConnect.setIdToken(idToken)
        return {
          type: 'login-success',
          title: 'Login success',
          message: 'You are logged in 🔥🔥',
          code: 200,
          data: userCredential.user,
          tokenResponse: userCredential._tokenResponse,
        }
      })
      .catch((error: any) => {
        return {
          type: 'login-error',
          title: 'Login error',
          message: error.message,
          code: error.code,
          data: error,
        }
      })
  }

  const logoutUser = async () => {
    const router = useRouter()
    const toaster = useToaster()
    return await signOut(auth)
      .then(() => {
        toaster.clearAll()
        toaster.show({
          title: 'Success',
          message: `Account successfully logged out!`,
          color: 'success',
          icon: 'ph:lock-open-fill',
          closable: true,
        })
        router.push('/auth/login')
        // Sign-out successful.
        return true
      })
      .catch((error: any) => {
        // An error happened.
        toaster.clearAll()
        toaster.show({
          title: 'Error',
          message: error.message,
          color: 'danger',
          icon: 'ph:user-circle-fill',
          closable: true,
        })
        return error
      })
  }

  const recoverPassword = async (email: string) => {
    return await sendPasswordResetEmail(auth, email)
      .then(() => {
        // Email sent.
        return true
      })
      .catch((error: any) => {
        // An error happened.
        return error
      })
  }

  return {
    registerFirebaseUser,
    logoutUser,
    loginFirebaseUser,
    recoverPassword,
  }
}
