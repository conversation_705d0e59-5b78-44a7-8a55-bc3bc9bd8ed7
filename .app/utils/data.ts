import { showErrors, showSuccess } from "./notification"

// Remove the direct call to useAccount here
// const { user, workspace, profile } = useAccount()

export const getLink = () => {
  // Move the useAccount call inside the function
  const { user, workspace, profile } = useAccount()
  return {
    userId: user.value.uid,
    workspaceId: workspace.value?.uid ?? "",
    profileId: profile.value?.uid ?? "",
  }
}

export const removeLink = (payload: any) => {
  delete payload.userId
  delete payload.workspaceId
  delete payload.profileId
  delete payload.updatedAt
  delete payload.agencyId
  delete payload.projectId
  return payload
}

export const makeUUID = () => {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export const createItem = (payload: any, arr: any) => {
  try {
    payload = removeUndefined(payload)
    if (arr.length > 0) {
      const embeddingText = createEmbeddingText(payload, arr)
      payload.embeddingText = embeddingText
    }
    return payload
  } catch (err) {
    showErrors(err)
    return err
  }
}

export const createItemAll = async (func: any, payload: any, arr: any) => {
  try {
    payload = removeUndefined(payload)
    if (arr.length > 0) {
      const embeddingText = createEmbeddingText(payload, arr)
      payload.embeddingText = embeddingText
    }
    return await func(payload)
  } catch (err) {
    showErrors(err)
    return err
  }
}

export const updateItem = (payload: any, arr: any) => {
  try {
    payload = removeLink(payload)
    payload = removeUndefined(payload)

    if (arr.length > 0) {
      const embeddingText = createEmbeddingText(payload, arr)
      payload.embeddingText = embeddingText
    }
    return payload
  } catch (err) {
    showErrors(err)
    return err
  }
}

export const convertObjectToArray = (inputObject: any) => {
  // Assuming the inputObject has a single key "0"
  return Object.values(inputObject)
}
export const removeUndefined = (obj: { [x: string]: any }) => {
  const newObj: any = {}

  Object.keys(obj).forEach((key) => {
    if (Array.isArray(obj[key])) {
      if (obj[key] === null || key === "place") {
        newObj[key] = convertObjectToArray(obj[key])
      } else {
        newObj[key] = obj[key]
      }
    } else if (obj[key] !== undefined && obj[key] !== null) {
      newObj[key] = obj[key]
    } else if (obj[key] === Object(obj[key])) {
      newObj[key] = removeUndefined(obj[key])
    } else if (obj[key] !== undefined && obj[key] !== null) {
      newObj[key] = obj[key]
    }
  })
  return newObj
}

export const createEmbeddingText = (formData: any, k: any) => {
  let text = ""
  if (formData.embedding) delete formData.embedding
  // loop over formData and if the form data contains a key that is in the schema, add it to the text string
  function processItem(key: string, item: any): string {
    let text = ""
    if (Array.isArray(item)) {
      item.forEach((subItem: any, index: number) => {
        text += processItem(` ${key}[${index}]`, subItem)
      })
    } else if (typeof item === "object" && item !== null) {
      for (const subKey in item) {
        if (item[subKey] !== undefined) {
          text += processItem(` ${key}.${subKey}`, item[subKey])
        }
      }
    } else {
      text += `${key}: ${item} `
    }
    return text
  }

  for (const key in formData) {
    if (k && formData[key] !== undefined && k.includes(key)) {
      text += processItem(key, formData[key])
    }
  }
  return text
}
