
export const showErrors = (error: any) => {
  const toaster = useToaster()

  console.log("show error", error)
  console.log("show error", error.message)
  console.log("show error", toaster)

  toaster.show({
    title: "Oops!",
    message: error.message ? error.message : error,
    type: "error",
    color: "danger",
    icon: "lucide:alert-triangle",
    closable: true,
  })
}

export const showSuccess = (message: any) => {
  const toaster = useToaster()

  toaster.show({
    title: "Success!",
    message: message ? message : "success",
    type: "error",
    color: "success",
    icon: "lucide:alert-triangle",
    closable: true,
  })
}
