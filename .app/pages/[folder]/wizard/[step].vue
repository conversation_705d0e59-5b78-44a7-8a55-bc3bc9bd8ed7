<template>
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-4">Wizard for {{ route.params.folder }}</h1>
    <p class="text-gray-600">
      This is the wizard page for the <strong>{{ route.params.folder }}</strong> section.
      You can customize this page with your wizard logic, forms, or steps.
    </p>
    <h2 class="text-xl font-bold mb-4">Step: {{ route.params.step }}</h2>
    <!-- Add your wizard components, steps, or forms here -->
    <div class="mt-6">
      <button class="bg-blue-500 text-white px-4 py-2 rounded">Start Wizard</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
// Access the dynamic folder parameter
const route = useRoute()
const folder = route.params.folder
const step = route.params.step

// You can add reactive data, methods, or lifecycle hooks here
// For example, fetch data based on the folder type
console.log(`Loading wizard for: ${folder}`)
</script>

<style scoped>
/* Add any scoped styles here */
</style>