<script setup lang="ts">
const route = useRoute()
const folder = route.params.folder
const step = route.params.step
console.log(`Loading wizard for: ${folder}`)
</script>

<template>
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-4">
      Wizard for {{ $route.params.folder || 'default' }}
    </h1>
    <p class="text-gray-600">
      This is the wizard page for the <strong>{{ $route.params.folder || 'default' }}</strong> section.
      You can customize this page with your wizard logic, forms, or steps.
    </p>
    <h2 class="text-xl font-bold mb-4">
      Step: {{ $route.params.step }}
    </h2>
    <!-- Add your wizard components, steps, or forms here -->
    <div class="mt-6">
      <button class="bg-blue-500 text-white px-4 py-2 rounded">
        Start Wizard
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Add any scoped styles here */
</style>
