<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.2.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 369 369" style="enable-background:new 0 0 369 369;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:#8B40E2;}
	.st2{clip-path:url(#SVGID_2_);fill:#FFFFFF;}
</style>
<circle class="st0" cx="184.5" cy="184.5" r="184.5"/>
<circle class="st1" cx="184.5" cy="184.5" r="175.8"/>
<g>
	<defs>
		<circle id="SVGID_1_" cx="184.5" cy="184.5" r="175.8"/>
	</defs>
	<clipPath id="SVGID_2_">
		<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
	</clipPath>
	<path class="st2" d="M-101.8,8.7l263.6,220l13.6-131l120.8,98.6L456.1,45L301.3,288.2L219.6,210l-17,100.2
		c0,0-307.8-282.2-311.2-273.7S-101.8,8.7-101.8,8.7z"/>
</g>
</svg>
