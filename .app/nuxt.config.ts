import { defineNuxtConfig } from 'nuxt/config'

export default defineNuxtConfig({
  compatibilityDate: '2025-09-21',
  future: {
    compatibilityVersion: 4,
  },
  // Disable type checking during dev to avoid external checker worker spawns
  typescript: {
    typeCheck: false,
  },
  extends: [
    /**
     * This extends the base Tairo layer.
     *
     * Alternatively you can use the following:
     * ["gh:cssninjaStudio/tairo/layers/tairo#v1.4.0", {
     *    install: true,
     *    auth: import.meta.env.GITHUB_TOKEN,
     * }]
     *
     * @see https://github.com/unjs/c12#extending-config-layer-from-remote-sources
     *
     * This would allows you to create an empty git repository
     * with only your source code and no demo.
     */

    '../layers/shared',
  ],

  css: [
    /**
     * Load Tailwind CSS
     */
    '~/assets/main.css',
  ],
  modules: [
    'reka-ui/nuxt',
    '@vueuse/nuxt',
    '@nuxtjs/i18n',
    // Temporarily disable modules to isolate EBADF spawns. Re-enable once fixed.
    // '@nuxt/image',
    // '@nuxt/fonts',
    // '@cssninja/nuxt-toaster',
  ],
  i18n: {
    defaultLocale: 'en',
  },
  nitro: {
    // Configure esbuild to avoid service mode and spawn issues
    esbuild: {
      options: {
        // Disable service mode to avoid spawn issues
        logLevel: 'silent',
      },
    },
    // Disable experimental features that might cause spawn issues
    experimental: {
      wasm: false,
    },
    // Alternative: try to use different minification
    minify: false,
  },
  vite: {
    // Optimize file watching to prevent spawn issues
    server: {
      watch: {
        usePolling: false,
        useFsEvents: true,
      },
    },
  },
})
