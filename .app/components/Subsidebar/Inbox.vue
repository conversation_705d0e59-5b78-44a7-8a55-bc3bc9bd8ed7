<script setup lang="ts">
const route = useRoute()
const activeId = ref(1)
const { data } = useFetch('/api/inbox')

watch(() => route.path, () => {
  if (route.path.startsWith('/dashboards/inbox')) {
    activeId.value = Number(route.params.id) || 1
  }
}, { immediate: true })
</script>

<template>
  <TairoSidebarSubsidebarContent>
    <div class="flex h-12 w-full items-center justify-center shrink-0">
      <BaseButton size="sm" rounded="full" variant="outline">
        <Icon name="lucide:plus" class="size-4" />
      </BaseButton>
    </div>
    <div class="flex h-12 w-full items-center justify-center shrink-0">
      <BaseTooltip
        content="Received"
        variant="dark"
        :bindings="{
          content: { side: 'left' },
          portal: { disabled: true },
        }"
      >
        <BaseButton size="sm" rounded="md" variant="outline">
          <Icon name="solar:inbox-linear" class="size-5" />
        </BaseButton>
      </BaseTooltip>
    </div>
    <div class="flex h-12 w-full items-center justify-center shrink-0">
      <BaseTooltip
        content="Sent"
        variant="dark"
        :bindings="{
          content: { side: 'left' },
          portal: { disabled: true },
        }"
      >
        <BaseButton size="sm" rounded="md" variant="outline">
          <Icon name="solar:inbox-out-linear" class="size-5" />
        </BaseButton>
      </BaseTooltip>
    </div>
    <div class="flex h-12 w-full items-center justify-center shrink-0">
      <BaseTooltip
        content="Important"
        variant="dark"
        :bindings="{
          content: { side: 'left' },
          portal: { disabled: true },
        }"
      >
        <BaseButton size="sm" rounded="md" variant="outline">
          <Icon name="solar:bookmark-linear" class="size-5" />
        </BaseButton>
      </BaseTooltip>
    </div>
    <div class="flex h-12 w-full items-center justify-center shrink-0">
      <BaseTooltip
        content="Spam"
        variant="dark"
        :bindings="{
          content: { side: 'left' },
          portal: { disabled: true },
        }"
      >
        <BaseButton size="sm" rounded="md" variant="outline">
          <Icon name="solar:trash-bin-trash-linear" class="size-5" />
        </BaseButton>
      </BaseTooltip>
    </div>
    <div class="flex h-12 w-full items-center justify-center shrink-0">
      <BaseTooltip
        content="Calendar"
        variant="dark"
        :bindings="{
          content: { side: 'left' },
          portal: { disabled: true },
        }"
      >
        <BaseButton size="sm" rounded="md" variant="outline">
          <Icon name="solar:calendar-linear" class="size-5" />
        </BaseButton>
      </BaseTooltip>
    </div>
  </TairoSidebarSubsidebarContent>
</template>
