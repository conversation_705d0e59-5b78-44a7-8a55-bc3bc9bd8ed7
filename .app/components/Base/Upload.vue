<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    /**
     * The form input identifier.
     */
    id?: string
    item_id?: string
    /**
     * The type of input.
     */
    type?: string
    value?: any

    /**
     * The radius of the input.
     *
     * @since 2.0.0
     * @default 'rounded'
     */
    rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'

    /**
     * The size of the input.
     *
     * @default 'md'
     */
    size?: 'sm' | 'md' | 'lg'

    /**
     * The contrast of the input.
     *
     * @default 'default'
     */
    contrast?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast'

    /**
     * The label to display for the input.
     */
    label?: string

    /**
     * If the label should be floating.
     */
    labelFloat?: boolean

    /**
     * The icon to display for the input.
     */
    icon?: string

    /**
     * The placeholder to display for the input.
     */
    placeholder?: string

    /**
     * An error message or boolean value indicating whether the input is in an error state.
     */
    error?: string | boolean

    /**
     * Whether the color of the input should change when it is focused.
     */
    colorFocus?: boolean

    /**
     * Whether the input is in a loading state.
     */
    loading?: boolean

    /**
     * Optional CSS classes to apply to the wrapper, label, input, addon, error, and icon elements.
     */
    classes?: {
      /**
       * CSS classes to apply to the wrapper element.
       */
      wrapper?: string | string[]

      /**
       * CSS classes to apply to the outer element.
       */
      outer?: string | string[]

      /**
       * CSS classes to apply to the label element.
       */
      label?: string | string[]

      /**
       * CSS classes to apply to the input element.
       */
      input?: string | string[]

      /**
       * CSS classes to apply to the addon element.
       */
      addon?: string | string[]

      /**
       * CSS classes to apply to the error element.
       */
      error?: string | string[]

      /**
       * CSS classes to apply to the icon element.
       */
      icon?: string | string[]
    }
  }>(),
  {
    id: undefined,
    type: 'text',
    rounded: undefined,
    size: undefined,
    contrast: undefined,
    label: undefined,
    icon: undefined,
    placeholder: undefined,
    error: false,
    classes: () => ({}),
    item_id: Math.random().toString(36).substring(7),
  },
)
const [modelValue, modelModifiers] = defineModel<any, 'lazy'>({
  set(value) {
    return value
  },
})
const imgUpload: any = ref([])
onMounted(() => {
  if (props.value) {
    modelValue.value = props.value
  }
})
const currentInstanceUploadProps: any = getCurrentInstance()

function emitChanges(data: any[]) {
  // remove duplicates
  imgUpload.value = [...new Set([...imgUpload.value, ...data])]
  console.log('data', data)
  currentInstanceUploadProps.emit('input', data)
  modelValue.value = data
}
const showAddItems = ref(false)
</script>

<template>
  <div class="w-full">
    <label :class="classes">
      {{ label }}
    </label>
    <file-button @upload-add="emitChanges" @close="showAddItems = false" />
    <div class="flex flex-wrap">
      <div v-for="(img, ind) in imgUpload" :key="ind" class="relative mt-2">
        <div
          class="mr-2 mt-2 flex h-32 content-center items-center justify-center overflow-hidden rounded"
        >
          <file-type :doc="img" classes="w-48 min-h-32" />
        </div>
      </div>
    </div>
  </div>
</template>
