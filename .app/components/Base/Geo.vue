<script setup lang="ts">
import { useGeolocation } from '@vueuse/core'

defineOptions({
  inheritAttrs: false,
})
const props = withDefaults(
  defineProps<{
    /**
     * The form input identifier.
     */
    id?: string
    item_id?: string
    /**
     * The type of input.
     */
    type?: string

    /**
     * The radius of the input.
     *
     * @since 2.0.0
     * @default 'rounded'
     */
    rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'

    /**
     * The size of the input.
     *
     * @default 'md'
     */
    size?: 'sm' | 'md' | 'lg'

    /**
     * The contrast of the input.
     *
     * @default 'default'
     */
    contrast?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast'

    /**
     * The label to display for the input.
     */
    label?: string

    /**
     * If the label should be floating.
     */
    labelFloat?: boolean

    /**
     * The icon to display for the input.
     */
    icon?: string

    /**
     * The placeholder to display for the input.
     */
    placeholder?: string

    /**
     * An error message or boolean value indicating whether the input is in an error state.
     */
    error?: string | boolean

    /**
     * Whether the color of the input should change when it is focused.
     */
    colorFocus?: boolean

    /**
     * Whether the input is in a loading state.
     */
    loading?: boolean

    /**
     * Optional CSS classes to apply to the wrapper, label, input, addon, error, and icon elements.
     */
    classes?: {
      /**
       * CSS classes to apply to the wrapper element.
       */
      wrapper?: string | string[]

      /**
       * CSS classes to apply to the outer element.
       */
      outer?: string | string[]

      /**
       * CSS classes to apply to the label element.
       */
      label?: string | string[]

      /**
       * CSS classes to apply to the input element.
       */
      input?: string | string[]

      /**
       * CSS classes to apply to the addon element.
       */
      addon?: string | string[]

      /**
       * CSS classes to apply to the error element.
       */
      error?: string | string[]

      /**
       * CSS classes to apply to the icon element.
       */
      icon?: string | string[]
    }
  }>(),
  {
    id: undefined,
    type: 'text',
    rounded: undefined,
    size: undefined,
    contrast: undefined,
    label: undefined,
    icon: undefined,
    placeholder: undefined,
    error: false,
    classes: () => ({}),
    item_id: Math.random().toString(36).substring(7),
  },
)
const { coords, locatedAt, error: errorLocation, resume, pause } = useGeolocation()

function looseToNumber(val: any) {
  const n = Number.parseFloat(val)
  return Number.isNaN(n) ? val : n
}

const [modelValue, modelModifiers] = defineModel<
  object | number,
  'lazy' | 'trim' | 'number'
>({
  set(value) {
    if (modelModifiers.number) {
      return looseToNumber(value)
    }
    else if (modelModifiers.trim && typeof value === 'string') {
      return value.trim()
    }

    return value
  },
})

const rounded = useNuiDefaultProperty(props, 'BaseInput', 'rounded')
const size = useNuiDefaultProperty(props, 'BaseInput', 'size')
const contrast = useNuiDefaultProperty(props, 'BaseInput', 'contrast')

const inputRef = ref<HTMLInputElement>()
const id = useNinjaId(() => props.id)

const radiuses = {
  none: '',
  sm: 'nui-input-rounded-sm',
  md: 'nui-input-rounded-md',
  lg: 'nui-input-rounded-lg',
  full: 'nui-input-rounded-full',
} as Record<string, string>

const sizes = {
  sm: 'nui-input-sm',
  md: 'nui-input-md',
  lg: 'nui-input-lg',
} as Record<string, string>

const contrasts = {
  'default': 'nui-input-default',
  'default-contrast': 'nui-input-default-contrast',
  'muted': 'nui-input-muted',
  'muted-contrast': 'nui-input-muted-contrast',
} as Record<string, string>

defineExpose({
  /**
   * The underlying HTMLInputElement element.
   */
  el: inputRef,

  /**
   * The internal id of the radio input.
   */
  id,
})

const placeholder = computed(() => {
  if (props.loading) {
    return
  }
  if (props.labelFloat) {
    return props.label
  }

  return props.placeholder
})

if (process.dev) {
  const slots = useSlots()
  if (props.labelFloat && 'label' in slots) {
    console.warn(
      '[ninja-ui][base-input] The "label-float" property is not compatible with the label slot, use the label property instead.',
    )
  }
}

const lat = ref(0)
const lng = ref(0)

watch(
  () => lat.value,
  (value: any) => {
    if (value) {
      console.log('lat', value)
      modelValue.value = { lat: value, lng: lng.value }
    }
  },
)

watch(
  () => lng.value,
  (value: any) => {
    if (value) {
      console.log('lng', value)
      modelValue.value = { lat: lat.value, lng: value }
    }
  },
)
function getGeoLocation() {
  resume()
  console.log('locatedAt', locatedAt.value)
  console.log('coords', coords.value)
  useNotification(
    'Location',
    'Getting your location',
    'info',
  )
  const timeoutId = setInterval(() => {
    console.log('locatedAt', locatedAt.value)
    console.log('coords', coords.value)
    console.log('coordslat', coords.value.latitude)
    console.log('errorLocation', errorLocation.value)
    if (errorLocation.value?.code) {
      useNotification(
        'Location',
        'Error getting your location',
        'warning',
      )
      clearInterval(timeoutId)
    }
    if (locatedAt.value) {
      lat.value = coords.value.latitude
      lng.value = coords.value.longitude
      clearInterval(timeoutId)
    }
  }, 1000)
}

const geoloc = ref({ lat: 0, lng: 0 })
watch(
  () => geoloc.value,
  (value: any) => {
    if (value) {
      console.log('geoloc', value)
      lat.value = value.lat
      lng.value = value.lng
    }
  },
)

const isOpen = ref(false)
</script>

<template>
  <div

    class="nui-input-wrapper"
    :class="[
      contrast && contrasts[contrast],
      size && sizes[size],
      rounded && radiuses[rounded],
      props.error && !props.loading && 'nui-input-error',
      props.loading && 'nui-input-loading',
      props.labelFloat && 'nui-input-label-float',
      props.icon && 'nui-has-icon',
      props.colorFocus && 'nui-input-focus',
      props.classes?.wrapper,
    ]"
  >
    <label
      v-if="
        ('label' in $slots && !props.labelFloat)
          || (props.label && !props.labelFloat)
      "
      class="nui-input-label"
      :for="id"
      :class="props.classes?.label"
    >
      <slot name="label">{{ props.label }}</slot>
    </label>

    <div class="flex space-x-2">
      <div class="nui-input-outer" :class="props.classes?.outer">
        <BaseInput
          v-model="lat"
          label="Latitude"
          type="number"
          :placeholder="placeholder"
        />
      </div>
      <div class="nui-input-outer" :class="props.classes?.outer">
        <BaseInput
          v-model="lng"
          label="Longitude"
          type="number"
          :placeholder="placeholder"
        />
      </div>
      <div class="mb-2 flex items-end">
        <BaseButtonIcon
          size="sm"
          rounded="full"
          @click="isOpen = !isOpen"
        >
          <Icon name="material-symbols:add-location-alt" class="size-5" />
        </BaseButtonIcon>
      </div>
    </div>
    <Modal
      :open="isOpen"
      size="md"
      footer-align="center"
      @close="isOpen = false"
    >
      <div class="nui-input flex w-full p-4">
        <label
          class="nui-input-label mr-4"
          :class="props.classes?.label"
        >
          <slot name="label">Get Current Location</slot>
        </label>
        <BaseButtonIcon
          size="sm"
          rounded="full"
          @click="getGeoLocation"
        >
          <Icon name="mdi:target" class="size-5" />
        </BaseButtonIcon>
      </div>
      <div class="flex w-full justify-center">
        OR
      </div>
      <div class="mt-2 p-4">
        <BaseGeoLocation v-model="geoloc" label="Search" />
      </div>
    </Modal>
  </div>
</template>
