<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    /**
     * The HTML element or component name to use for the paragraph.
     *
     * @default 'p'
     */
    as?: string
    item_id?: string
    /**
     * The size of the paragraph.
     *
     * @default 'md'
     */
    size?:
      | 'xs'
      | 'sm'
      | 'md'
      | 'lg'
      | 'xl'
      | '2xl'
      | '3xl'
      | '4xl'
      | '5xl'
      | '6xl'
      | '7xl'
      | '8xl'
      | '9xl'

    /**
     * The weight of the paragraph.
     *
     * @default 'normal'
     */
    weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold'

    /**
     * The lead of the paragraph.
     *
     * @default 'normal'
     */
    lead?: 'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose'
    innerText?: string
  }>(),
  {
    as: undefined,
    size: undefined,
    weight: undefined,
    lead: undefined,
    innerText: '',
    item_id: Math.random().toString(36).substring(7),
  },
)

const as = useNuiDefaultProperty(props, 'BaseParagraph', 'as')
const size = useNuiDefaultProperty(props, 'BaseParagraph', 'size')
const weight = useNuiDefaultProperty(props, 'BaseParagraph', 'weight')
const lead = useNuiDefaultProperty(props, 'BaseParagraph', 'lead')

const sizes = {
  'xs': 'nui-paragraph-xs',
  'sm': 'nui-paragraph-sm',
  'md': 'nui-paragraph-md',
  'lg': 'nui-paragraph-lg',
  'xl': 'nui-paragraph-xl',
  '2xl': 'nui-paragraph-2xl',
  '3xl': 'nui-paragraph-3xl',
  '4xl': 'nui-paragraph-4xl',
  '5xl': 'nui-paragraph-5xl',
  '6xl': 'nui-paragraph-6xl',
  '7xl': 'nui-paragraph-7xl',
  '8xl': 'nui-paragraph-8xl',
  '9xl': 'nui-paragraph-9xl',
} as Record<string, string>

const weights = {
  light: 'nui-weight-light',
  normal: 'nui-weight-normal',
  medium: 'nui-weight-medium',
  semibold: 'nui-weight-semibold',
  bold: 'nui-weight-bold',
  extrabold: 'nui-weight-extrabold',
} as Record<string, string>

const leads = {
  none: 'nui-lead-none',
  tight: 'nui-lead-tight',
  snug: 'nui-lead-snug',
  normal: 'nui-lead-normal',
  relaxed: 'nui-lead-relaxed',
  loose: 'nui-lead-loose',
} as Record<string, string>

const classes = computed(() => [
  'nui-paragraph',
  size.value && sizes[size.value],
  weight.value && weights[weight.value],
  lead.value && leads[lead.value],
])
</script>

<template>
  <component
    :is="props.as ? props.as : as"

    :class="classes"
  >
    {{ innerText }}
    <slot />
  </component>
</template>
