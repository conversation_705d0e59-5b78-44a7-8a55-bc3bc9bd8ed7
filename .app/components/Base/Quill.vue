<script setup lang="ts">
defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(
  defineProps<{
    /**
     * The form input identifier.
     */
    id?: string
    item_id?: string
    /**
     * The type of input.
     */
    type?: string

    theme?: string
    value?: any
    /**
     * The radius of the input.
     *
     * @since 2.0.0
     * @default 'rounded'
     */
    rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'

    /**
     * The size of the input.
     *
     * @default 'md'
     */
    size?: 'sm' | 'md' | 'lg'

    /**
     * The contrast of the input.
     *
     * @default 'default'
     */
    contrast?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast'

    /**
     * The label to display for the input.
     */
    label?: string

    /**
     * If the label should be floating.
     */
    labelFloat?: boolean

    /**
     * The icon to display for the input.
     */
    icon?: string

    /**
     * The placeholder to display for the input.
     */
    placeholder?: string

    /**
     * An error message or boolean value indicating whether the input is in an error state.
     */
    error?: string | boolean

    /**
     * Whether the color of the input should change when it is focused.
     */
    colorFocus?: boolean

    /**
     * Whether the input is in a loading state.
     */
    loading?: boolean

    /**
     * Optional CSS classes to apply to the wrapper, label, input, addon, error, and icon elements.
     */
    classes?: {
      /**
       * CSS classes to apply to the wrapper element.
       */
      wrapper?: string | string[]

      /**
       * CSS classes to apply to the outer element.
       */
      outer?: string | string[]

      /**
       * CSS classes to apply to the label element.
       */
      label?: string | string[]

      /**
       * CSS classes to apply to the input element.
       */
      input?: string | string[]

      /**
       * CSS classes to apply to the addon element.
       */
      addon?: string | string[]

      /**
       * CSS classes to apply to the error element.
       */
      error?: string | string[]

      /**
       * CSS classes to apply to the icon element.
       */
      icon?: string | string[]
    }
  }>(),
  {
    id: undefined,
    type: 'text',
    rounded: undefined,
    size: undefined,
    contrast: undefined,
    label: undefined,
    icon: undefined,
    placeholder: undefined,
    error: false,
    classes: () => ({}),
    item_id: Math.random().toString(36).substring(7),
    theme: 'snow',
    colorFocus: true,
    loading: false,
    labelFloat: false,
  },
)

function looseToNumber(val: any) {
  const n = Number.parseFloat(val)
  return Number.isNaN(n) ? val : n
}

const [modelValue, modelModifiers] = defineModel<
  string | number,
  'lazy' | 'trim' | 'number'
>({
  set(value) {
    if (modelModifiers.number) {
      return looseToNumber(value)
    }
    else if (modelModifiers.trim && typeof value === 'string') {
      return value.trim()
    }

    return value
  },
})

const rounded = useNuiDefaultProperty(props, 'BaseInput', 'rounded')
const size = useNuiDefaultProperty(props, 'BaseInput', 'size')
const contrast = useNuiDefaultProperty(props, 'BaseInput', 'contrast')

const inputRef = ref<HTMLInputElement>()
const id = useNinjaId(() => props.id)

const radiuses = {
  none: '',
  sm: 'nui-input-rounded-sm',
  md: 'nui-input-rounded-md',
  lg: 'nui-input-rounded-lg',
  full: 'nui-input-rounded-full',
} as Record<string, string>

const sizes = {
  sm: 'nui-input-sm',
  md: 'nui-input-md',
  lg: 'nui-input-lg',
} as Record<string, string>

const contrasts = {
  'default': 'nui-input-default',
  'default-contrast': 'nui-input-default-contrast',
  'muted': 'nui-input-muted',
  'muted-contrast': 'nui-input-muted-contrast',
} as Record<string, string>

defineExpose({
  /**
   * The underlying HTMLInputElement element.
   */
  el: inputRef,

  /**
   * The internal id of the radio input.
   */
  id,
})

const placeholder = computed(() => {
  if (props.loading) {
    return
  }
  if (props.labelFloat) {
    return props.label
  }

  return props.placeholder
})

if (process.dev) {
  const slots = useSlots()
  if (props.labelFloat && 'label' in slots) {
    console.warn(
      '[ninja-ui][base-input] The "label-float" property is not compatible with the label slot, use the label property instead.',
    )
  }
}

const content: any = ref('')
const editorOption: any = ref({
  theme: 'snow', // snow or
})
const toolbarQuill: any = ref([
  ['bold', 'italic', 'underline', 'strike'], // toggled buttons
  ['blockquote', 'code-block'],
  ['markdown'],
  [{ header: 1 }, { header: 2 }], // custom button values
  [{ list: 'ordered' }, { list: 'bullet' }],
  [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
  [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
  [{ direction: 'rtl' }], // text direction

  [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
  [{ header: [1, 2, 3, 4, 5, 6, false] }],

  [{ color: [] }, { background: [] }], // dropdown with defaults from theme
  [{ font: [] }],
  [{ align: [] }],

  ['clean'],
])

const newDescription = computed(() => {
  let newDisc = ''
  if (content.value) {
    newDisc = content.value.replace('<ul>', '<ul class="list-disc">')
    newDisc = newDisc.replace('<ol>', '<ol class="list-decimal">')
  }
  return newDisc
})
// watch(() => quillProps.schema.placeholder, () => content.value = quillProps.schema.placeholder)
onMounted(() => {
  if (props.value) {
    modelValue.value = props.value
  }
  // if (quillProps.schema.edit)
  //   content.value = quillProps.schema.placeholder

  // if (quillProps.schema.editorOptions)
  //   editorOption.value = quillProps.schema.editorOptions
  editorOption.value.theme = props.theme
  // if (quillProps.schema.toolbar)
  //   toolbarQuill.value = quillProps.schema.toolbar
})
const inputsQuillInstance: any = getCurrentInstance()
if (content.value) {
  watch(() => content.value, () => {
    quillInput()
  })
}

function quillInput() {
//   inputsQuillInstance.emit("input", newDescription.value, quillProps.schema.selectedIndex)
}
</script>

<template>
  <div

    class="nui-input-wrapper h-full"
    :class="[
      contrast && contrasts[contrast],
      size && sizes[size],
      rounded && radiuses[rounded],
      props.error && !props.loading && 'nui-input-error',
      props.loading && 'nui-input-loading',
      props.labelFloat && 'nui-input-label-float',
      props.icon && 'nui-has-icon',
      props.colorFocus && 'nui-input-focus',
      props.classes?.wrapper,
    ]"
  >
    <label
      v-if="
        ('label' in $slots && !props.labelFloat)
          || (props.label && !props.labelFloat)
      "
      class="nui-input-labels shadow-lg"
      :for="id"
      :class="props.classes?.label"
    >
      <slot name="label">{{ props.label }}</slot>
    </label>
    <div :class="{ 'nui-input-outer': editorOption.theme == 'bubble' }" class="nui-input-outer h-full">
      <client-only>
        <quill-editor
          v-model:content="modelValue"
          :theme="editorOption.theme"
          content-type="html"
          :toolbar="toolbarQuill"
          class="border-bottom-color nui-icon-outer h-full"
          @input="quillInput"
        />
      </client-only>
    </div>
  </div>
</template>

<style scoped>
.container {
  width: 60%;
  margin: 0 auto;
  padding: 50px 0;
}

.container .quill-editor {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.ql-toolbar {
  border: 1px solid #a0a2a42e !important;
  border-radius: 5px !important;
}
</style>
