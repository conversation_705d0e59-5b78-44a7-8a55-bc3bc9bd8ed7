<script setup lang="ts">
const props: any = withDefaults(
  defineProps<{
    createRoute?: string
    bulkRoute?: string
    editRoute?: string
    dashboardRoute?: string
    collection?: string
    schema?: any
    start?: any
  }>(),
  {
    createRoute: '/snake/competition/create',
    bulkRoute: '/snake/competition/bulk-create',
    editRoute: '/snake/competition/individual/edit',
    dashboardRoute: '/snake/competition/individual/dashboard',
    collection: 'snakeCompetition',
    schema: {},
    start: {},
  },
)

const { coupon } = useCrm()
const router = useRouter()
const page = ref(1)
const filter = ref('')
const perPage = ref(10)
const totalItems = ref(0)
const data: any = ref([])

function updatePage(pg: number) {
  page.value = pg
  getData()
}
watch([filter, perPage], () => {
  router.push({
    query: {
      page: undefined,
    },
  })
})

const query = computed(() => {
  return {
    filter: filter.value,
    perPage: perPage.value,
    page: page.value,
  }
})
const lastVisible: any = ref(null)
async function getData() {
  // if (page.value === 1) {
  //   let pagnationData = await getPaginationWhere(
  //     props.collection,
  //     '_additional.createdAt',
  //     perPage.value,
  //   )
  //   data.value = pagnationData.data
  //   lastVisible.value = pagnationData.lastVisible
  //   let count = await countColWhere(props.collection)
  //   totalItems.value = count
  // }
  // else {
  //   let nextData: any = await getPaginationNextWhere(
  //     props.collection,
  //     '_additional.createdAt',
  //     perPage.value,
  //     lastVisible.value,
  //   )
  //   data.value = nextData.data
  //   lastVisible.value = nextData.lastVisible
  // }
}

onMounted(() => {
  getData()
})
getData()

const selected = ref<number[]>([])

const isAllVisibleSelected = computed(() => {
  return selected.value.length === data.value?.length
})

function toggleAllVisibleSelection() {
  if (isAllVisibleSelected.value) {
    selected.value = []
  }
  else {
    selected.value = data.value?.map((item: any) => item.id) ?? []
  }
}

const em: any = getCurrentInstance()
function goTo(acc: any) {
  em.emit('selected', acc)
  router.push(props.dashboardRoute)
}

function goEdit(acc: any) {
  em.emit('selected', acc)
  router.push(props.editRoute)
}

async function remove(index: any) {
  console.log('index', index)
}

const listItems = computed(() => {
  return props.schema.fields.filter((field: any) => field.list)
})
</script>

<template>
  <div>
    <ContentWrapper>
      <template #left>
        <BaseInput
          v-model="filter"
          icon="lucide:search"
          placeholder="Filter coupons..."
          :classes="{
            wrapper: 'w-full sm:w-auto',
          }"
        />
      </template>
      <template #right>
        <BaseSelect
          v-model="perPage"
          label=""
          :classes="{
            wrapper: 'w-full sm:w-40',
          }"
        >
          <option :value="10">
            10 per page
          </option>
          <option :value="25">
            25 per page
          </option>
          <option :value="50">
            50 per page
          </option>
          <option :value="100">
            100 per page
          </option>
        </BaseSelect>
        <TableActions
          :create-route="{
            show: true,
            title: 'Add Coupon',
            text: 'Add a Coupon to your account',
            url: createRoute,
          }"
          :bulk-route="{
            show: true,
            title: 'Bulk Import Coupon',
            text: 'Bulk add a Coupon to your account',
            url: bulkRoute,
          }"
          :refresh-data="{
            show: true,
            title: 'Refresh',
            text: 'Refresh data',
          }"
          :export-data="{
            show: true,
            title: 'Export',
            text: 'Export Data to XLSX',
            dataTitle: 'Coupons',
            data,
          }"
          @refresh="getData"
        />
      </template>
      <div>
        <div v-if="data?.length === 0">
          <BasePlaceholderPage
            title="No matching results"
            subtitle="Looks like we couldn't find any matching results for your search terms. Try other search terms."
          >
            <template #image>
              <img
                class="block dark:hidden"
                src="/img/illustrations/placeholders/flat/placeholder-search-4.svg"
                alt="Placeholder image"
              >
              <img
                class="hidden dark:block"
                src="/img/illustrations/placeholders/flat/placeholder-search-4-dark.svg"
                alt="Placeholder image"
              >
            </template>
          </BasePlaceholderPage>
        </div>
        <div v-else>
          <client-only>
            <div class="relative mt-10 mb-12 w-full">
              <TransitionGroup
                enter-active-class="transform-gpu"
                enter-from-class="opacity-0 -translate-x-full"
                enter-to-class="opacity-100 translate-x-0"
                leave-active-class="absolute transform-gpu"
                leave-from-class="opacity-100 translate-x-0"
                leave-to-class="opacity-0 -translate-x-full"
              >
                <FlexTableRow
                  v-for="(item, index) in data"
                  :key="index"
                  shape="curved"
                  spaced
                  hoverable
                >
                  <template #start>
                    <FlexTableStart
                      :label="start.label"
                      :hide-label="index > 0"
                      :title="item[props.start.title]"
                      :subtitle="item[props.start.subtitle]"
                      :avatar="item[props.start.avatar]"
                      :badge="item[props.start.badge]"
                      initials=""
                      spaced
                    />
                  </template>
                  <template #end>
                    <FlexTableCell
                      v-for="(lst, inx) in listItems"
                      :key="inx"
                      :label="lst.label"
                      :hide-label="index > 0"
                      class="w-full sm:w-40"
                    >
                      <span
                        class="font-sans text-sm text-muted-500 dark:text-muted-400"
                      >
                        {{ item[lst.name] }}
                      </span>
                    </FlexTableCell>
                    <FlexTableCell
                      label="Discount Type"
                      :hide-label="index > 0"
                      class="sm:w-40"
                      spaced
                    >
                      <span
                        class="font-sans text-sm text-muted-500 dark:text-muted-400"
                      >
                        {{ item.discount_type }}
                      </span>
                    </FlexTableCell>
                    <FlexTableCell
                      label="action"
                      :hide-label="index > 0"
                    >
                      <div class="">
                        <div class="flex space-x-2">
                          <div
                            @click="goTo(item)"
                          >
                            <div
                              class="flex justify-center items-center mr-1 bg-white rounded-full shadow-xl dark:bg-muted-700 shadow-muted-300/40 dark:shadow-muted-900/20 size-10"
                            >
                              <div
                                class="flex justify-center items-center rounded-full size-8 bg-blue-500/20"
                              >
                                <Icon
                                  name="ic:twotone-dashboard"
                                  class="text-blue-500 size-5"
                                />
                              </div>
                            </div>
                          </div>
                          <div
                            @click="goEdit(item)"
                          >
                            <div
                              class="flex justify-center items-center mr-1 bg-white rounded-full shadow-xl dark:bg-muted-700 shadow-muted-300/40 dark:shadow-muted-900/20 size-10"
                            >
                              <div
                                class="flex justify-center items-center rounded-full bg-success-500/20 size-8"
                              >
                                <Icon
                                  name="ic:twotone-edit"
                                  class="text-success-500 size-5"
                                />
                              </div>
                            </div>
                          </div>
                          <div class="w-full">
                            <Popover size="sm">
                              <div
                                class="flex justify-center items-center mr-1 bg-white rounded-full shadow-xl dark:bg-muted-700 shadow-muted-300/40 dark:shadow-muted-900/20 size-10"
                              >
                                <div
                                  class="flex justify-center items-center rounded-full bg-danger-500/20 size-8"
                                >
                                  <Icon
                                    name="ph:trash-duotone"
                                    class="text-danger-500 size-5"
                                  />
                                </div>
                              </div>

                              <template #content>
                                <PopoverContentDelete
                                  :title="item.code"
                                  :subtitle="item.position"
                                  text="Are you sure you want to delete?"
                                  icon="ph:trash-duotone"
                                  icon-color="danger"
                                  class="absolute top-0 right-0"
                                  @delete="remove(index)"
                                />
                              </template>
                            </Popover>
                          </div>
                        </div>
                      </div>
                    </FlexTableCell>
                    <FlexTableCell
                      label="action"
                      :hide-label="index > 0"
                    >
                      <div class="w-full">
                        <div
                          class="flex justify-center items-center mr-1 bg-white rounded-full shadow-xl dark:bg-muted-700 shadow-muted-300/40 dark:shadow-muted-900/20 size-10"
                          @click="remove(index)"
                        >
                          <div
                            class="flex justify-center items-center rounded-full bg-danger-500/20 size-8"
                          >
                            <Icon
                              name="ph:trash-duotone"
                              class="text-danger-500 size-5"
                            />
                          </div>
                        </div>
                      </div>
                    </FlexTableCell>
                  </template>
                </FlexTableRow>
              </TransitionGroup>
            </div>
          </client-only>
          <div class="mt-6">
            <BasePagination
              :total-items="totalItems"
              :item-per-page="perPage"
              :current-page="page"
              shape="curved"
              no-router
              @update:current-page="updatePage"
            />
          </div>
        </div>
      </div>
    </ContentWrapper>
  </div>
</template>
