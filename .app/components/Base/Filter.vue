<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    className: string
    withFields: string
    withWhere: any
    limit?: number
    offset?: number
    displayValues?: any
    filterValue?: string
    item_id?: string
  }>(),
  {
    limit: 10,
    offset: 0,
    item_id: Math.random().toString(36).substring(7),
  },
)
const items = ref([])

const search = ref('')
const itemSelected = ref(false)

const filteredCustomers: any = computed(() => {
  if (!search.value) {
    return []
  }

  return items.value
    .filter((item: any) => {
      return (
        item[props.displayValues[0]]?.match(new RegExp(search.value, 'i'))
        || item[props.displayValues[1]]?.match(new RegExp(search.value, 'i'))
      )
    })
    .splice(0, 4)
})

const { SearchPaginateAll } = useWeaviateSearch()

watch(
  () => search.value,
  async () => {
    let withWhere = props.withWhere

    withWhere = {
      operator: 'And',
      operands: [
        ...withWhere.operands,
        {
          path: [props.filterValue],
          operator: 'GreaterThanEqual',
          valueText: search.value,
        },
      ],
    }
    console.log('withWhere', withWhere)
    const rec = await SearchPaginateAll(
      props.className,
      props.withFields,
      withWhere,
      props.limit,
      props.offset,
    )
    items.value = rec
    console.log(rec)
  },
)

const em: any = getCurrentInstance()?.emit
const item: any = ref(null)
function selectCustomer(itemData: any) {
  em('selected', itemData)
  itemSelected.value = true
  item.value = itemData
  search.value = ''
}

function dismissCustomer() {
  itemSelected.value = false
  item.value = ''
}
</script>

<template>
  <div class="relative">
    <BaseInput
      v-if="!itemSelected"
      v-model="search"
      icon="lucide:search"
      shape="curved"
      placeholder="ex: Figma, Github, ..."
      :classes="{
        input: 'h-12 text-base !ps-12',
        icon: 'h-12 w-12',
      }"
    />
    <div v-else class="flex items-center gap-3">
      <!-- <BaseAvatar
        size="sm"
        :src="project.item?.logo"
        class="bg-muted-100 dark:bg-muted-700/60"
      /> -->
      <div class="flex flex-col">
        <h3
          class="text-muted-800 dark:text-muted-100 font-sans text-sm font-semibold"
        >
          {{ item[displayValues[0]] }}
        </h3>
        <p
          v-if="Array.isArray(item[displayValues[1]])"
          class="text-muted-500 dark:text-muted-400 font-sans text-xs"
        >
          <template v-for="element in item[displayValues[1]]">
            <span>{{ element }}</span>
          </template>
        </p>
        <p v-else class="text-muted-500 dark:text-muted-400 font-sans text-xs">
          {{ item[displayValues[1]] }}
        </p>
      </div>
      <div class="me-3 ms-auto">
        <BaseButtonIcon
          small
          shape="full"
          @click="dismissCustomer"
        >
          <Icon name="lucide:x" class="size-4" />
        </BaseButtonIcon>
      </div>
    </div>

    <div
      class="border-muted-200 dark:border-muted-700 dark:bg-muted-800 shadow-muted-300/30 dark:shadow-muted-900/30 absolute start-0 top-14 w-full rounded-xl border bg-white p-4 shadow-xl transition-all duration-300"
      :class="
        search.length > 0
          ? 'opacity-100 translate-y-0'
          : 'opacity-0 pointer-events-none translate-y-1'
      "
    >
      <!-- Results -->
      <div
        v-if="filteredCustomers.length > 0"
        class="slimscroll max-h-[248px] space-y-2 overflow-y-auto"
      >
        <!-- Result -->
        <div
          v-for="item in filteredCustomers"
          :key="item.name"
          role="button"
          class="hover:bg-muted-100 flex cursor-pointer items-center gap-3 rounded-lg px-3 py-2"
        >
          <BaseAvatar size="sm" :src="item.logo" />
          <div class="flex flex-col">
            <h3
              class="text-muted-800 dark:text-muted-100 font-sans text-sm font-semibold"
            >
              {{ item[displayValues[0]] }}
            </h3>
            <p
              v-if="Array.isArray(item[displayValues[1]])"
              class="text-muted-500 dark:text-muted-400 font-sans text-xs"
            >
              <template v-for="element in item[displayValues[1]]">
                <span>{{ element }}</span>
              </template>
            </p>
            <p
              v-else
              class="text-muted-500 dark:text-muted-400 font-sans text-xs"
            >
              {{ item[displayValues[1]] }}
            </p>
          </div>
          <div class="ms-auto">
            <BaseButtonIcon
              small
              shape="full"
              @click="selectCustomer(item)"
            >
              <Icon name="lucide:plus" class="size-4" />
            </BaseButtonIcon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
