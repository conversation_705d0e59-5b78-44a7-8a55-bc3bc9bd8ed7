<script setup lang="ts">
const props: any = withDefaults(
  defineProps<{
    createRoute?: string
    collection?: string
  }>(),
  {
    createRoute: '/crm/accounts/create',
    collection: 'accounts',
  },
)
const { teamMember } = useAccount()
</script>

<template>
  <div>
    <!-- Header -->
    <div class="mb-8 flex flex-col justify-between md:flex-row md:items-center">
      <div
        class="ltablet:max-w-full flex max-w-[425px] flex-col items-center gap-4 text-center md:flex-row md:text-left lg:max-w-full"
      >
        <AvatarContact size="lg" />
        <div>
          <BaseHeading
            as="h2"
            size="xl"
            weight="light"
            lead="tight"
            class="text-muted-800 dark:text-white"
          >
            <span>Welcome back, {{ teamMember.username }}</span>
          </BaseHeading>
          <BaseParagraph>
            <span class="text-muted-500">
              Happy to see you again on your dashboard.
            </span>
          </BaseParagraph>
        </div>
      </div>
      <div
        class="mt-4 flex items-center justify-center gap-2 md:mt-0 md:justify-start"
      >
        <BaseButton>
          <span>View Reports</span>
        </BaseButton>
        <BaseButton color="primary" @click="$router.push(createRoute)">
          <span class="uppercase">Create {{ collection }}</span>
        </BaseButton>
      </div>
    </div>
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-6">
      <!-- Quick stats -->
      <StatsQuick :collection="collection" />
      <!-- Area Chart card -->
      <div class="ltablet:col-span-6 col-span-12 lg:col-span-6">
        <StatsArea :collection="collection" />
      </div>
      <!-- CTA card -->
      <div
        class="ptablet:col-span-6 ltablet:col-span-4 col-span-12 lg:col-span-4"
      >
        <CardMotivation :collection="collection" text="whoo" />
      </div>
      <!-- Radial Bar card -->
      <div
        class="ptablet:col-span-6 ltablet:col-span-4 col-span-12 lg:col-span-4"
      >
        <StatsEfficiency :collection="collection" />
      </div>
      <!-- Bar chart card -->
      <div class="ltablet:col-span-4 col-span-12 lg:col-span-4">
        <StatsBar :collection="collection" />
      </div>
    </div>
    <BaseCard class="relative mt-6 p-6">
      <div class="mb-6">
        <BaseHeading
          as="h3"
          size="md"
          weight="semibold"
          lead="tight"
          class="text-muted-800 dark:text-white"
        >
          <span class="uppercase">{{ collection }} List</span>
        </BaseHeading>
      </div>

      <slot name="lists" />
    </BaseCard>
  </div>
</template>
