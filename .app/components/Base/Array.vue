<script setup lang="ts">
defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(
  defineProps<{
    /**
     * The form input identifier.
     */
    id?: string

    type?: string
    item_id?: string

    /**
     * The radius of the select input.
     *
     * @since 2.0.0
     * @default 'sm'
     */
    rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'

    /**
     * The size of the select input.
     *
     * @default 'md'
     */
    size?: 'sm' | 'md' | 'lg'

    /**
     * The contrast of the select input.
     *
     * @default 'default'
     */
    contrast?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast'

    /**
     * The label text for the select input.
     */
    label?: string

    /**
     * If the label should be floating.
     */
    labelFloat?: boolean

    /**
     * An icon to display in the select input.
     */
    icon?: string

    /**
     * The placeholder to display for the select input.
     */
    placeholder?: string

    /**
     * Whether the select input is in a loading state.
     */
    loading?: boolean

    /**
     * Whether the select input is disabled.
     */
    disabled?: boolean

    /**
     * Whether the color of the input should change when it is focused.
     */
    colorFocus?: boolean

    /**
     * An error message to display, or a boolean indicating whether there is an error.
     */
    error?: string | boolean
    value?: any

    options?: any
    name: any
    class?: string

    /**
     * Classes to apply to the select input.
     */
    classes?: {
      /**
       * A class or classes to apply to the wrapper element.
       */
      wrapper?: string | string[]

      /**
       * A class or classes to apply to the label element.
       */
      label?: string | string[]

      /**
       * A class or classes to apply to the select element.
       */
      select?: string | string[]

      /**
       * A class or classes to apply to the chevron element.
       */
      chevron?: string | string[]

      /**
       * A class or classes to apply to the icon element.
       */
      icon?: string | string[]

      /**
       * A class or classes to apply to the error element.
       */
      error?: string | string[]
    }
  }>(),
  {
    id: undefined,
    rounded: undefined,
    size: undefined,
    contrast: undefined,
    label: '',
    icon: undefined,
    placeholder: '',
    name: '',
    error: false,
    classes: () => ({}),
    options: undefined,
    class: '',
    type: 'string',
    value: [],
    item_id: Math.random().toString(36).substring(7),

  },
)

const [modelValue] = defineModel<any>()

const rounded = useNuiDefaultProperty(props, 'BaseSelect', 'rounded')
const size = useNuiDefaultProperty(props, 'BaseSelect', 'size')
const contrast = useNuiDefaultProperty(props, 'BaseSelect', 'contrast')

const selectRef = ref<HTMLSelectElement>()
const id = useNinjaId(() => props.id)

const radiuses = {
  none: '',
  sm: 'nui-select-rounded-sm',
  md: 'nui-select-rounded-md',
  lg: 'nui-select-rounded-lg',
  full: 'nui-select-rounded-full',
} as Record<string, string>

const sizes = {
  sm: 'nui-select-sm',
  md: 'nui-select-md',
  lg: 'nui-select-lg',
} as Record<string, string>

const contrasts = {
  'default': 'nui-select-default',
  'default-contrast': 'nui-select-default-contrast',
  'muted': 'nui-select-muted',
  'muted-contrast': 'nui-select-muted-contrast',
} as Record<string, string>

defineExpose({
  /**
   * The underlying HTMLInputElement element.
   */
  el: selectRef,

  /**
   * The internal id of the radio input.
   */
  id,
})

const placeholder = computed(() => {
  if (props.loading) {
    return
  }
  if (props.labelFloat) {
    return props.label
  }

  return props.placeholder
})

watch(
  () => modelValue,
  (value) => {
    console.log('modelValue', props.name, value)
  },
)

const em = getCurrentInstance()
const edit = ref<any>([])
function updated_form(data: any, index: any) {
  console.log('data', data)
  console.log('index', index)
  // loop over data and update the edit.value.schema
  if (props.type === 'object') {
    // check if edit.value[index] is empty
    if (edit.value[index] === undefined || edit.value[index] === '') {
      edit.value[index] = {}
    }
    for (const key in data) {
      if (data[key] === undefined || data[key] === '')
        continue
      edit.value[index][key] = data[key]
    }
  }
  if (props.type === 'array') {
    edit.value = data
  }

  if (props.type === 'string') {
    edit.value = []
    for (const key in data) {
      edit.value.push(data[key])
    }
  }

  console.log('name', props.name)
  // edit.value.schema = data;
  em?.emit('array-updated', [props.name, edit.value])
}

function submit_form() {
  // here you have access to the validated form values

}

const all_values = ref<any>([])

onMounted(() => {
  // loop over initial values and setFieldValue
  for (const key in props.value) {
    all_values.value[key] = props.name[key]
  }

  if (Array.isArray(props.name) && props.name.length === 0 && props.type === 'text') {
    all_values.value = ['']
  }

  if (props.type === 'object') {
    const obj = {
      rules: [],
      superRefine: [],
      fields: props.options,
    }

    all_values.value = [obj]
  }
})

function addItem() {
  if (props.type === 'string') {
    all_values.value.push('')
  }

  if (props.type === 'object') {
    const obj = {
      rules: [],
      superRefine: [],
      fields: props.options,
    }

    all_values.value.push(obj)
  }

  if (props.type === 'array') {
    all_values.value.push(props.options)
  }
}

const schema = computed(() => {
  return {
    rules: [],
    superRefine: [],
    fields: [],
  }
  // if(props.value === undefined) {
  //   return
  // }else if (typeof props.value === 'string'){
  //   return
  // } else {
  //   return {
  //   rules: [],
  //   superRefine: [],
  //   fields: props.name.map((item: any) => {
  //     if (props.type === "string") {
  //       return props.options.map((option: any) => {
  //         return {
  //           label: option,
  //           name: option,
  //           as: "input",
  //           type: "text",
  //           placeholder: option,
  //           component: "BaseInput",
  //           shape: "curved",
  //           icon: "mdi:pen",
  //           value: item,
  //           required: true,
  //           disabled: false,
  //           class: "col-span-6",
  //         }
  //       })
  //     }

  //     else if (props.type === "object") {
  //       return props.options.map((option: any) => {
  //         return { ...option, value: item }
  //       })
  //     }

  //     else if (props.type === "array") {
  //       return props.options.map((option: any) => {
  //         return { ...option, value: item }
  //       })
  //     }
  //   }),
  // }
  // }
})
</script>

<template>
  <div

    class="nui-select-wrapper px-4"
    :class="[
      contrast && contrasts[contrast],
      size && sizes[size],
      rounded && radiuses[rounded],
      props.error && !props.loading && 'nui-select-error',
      props.loading && 'nui-select-loading',
      props.labelFloat && 'nui-select-label-float',
      props.icon && 'nui-has-icon',
      props.colorFocus && 'nui-select-focus',
      props.classes?.wrapper,
      props.class,
    ]"
  >
    <label
      v-if="
        ('label' in $slots && !props.labelFloat)
          || (props.label && !props.labelFloat)
      "
      class="nui-select-label flex w-full justify-between"
      :for="id"
      :class="props.classes?.label"
    >
      <slot name="label">{{ props.label }}</slot>

      <!-- <BaseButtonIcon
        size="sm"
        rounded="full"
        class="float-right"
        color="success"
        @click="addItem"
      > -->
      <Icon
        name="mdi:plus"
        class="hover:text-success-500 float-right size-4"
        @click="addItem"
      />
      <!-- </BaseButtonIcon> -->
    </label>

    <div
      v-for="(inp, index) in all_values"
      :key="index"
      class="w-full"
    >
      <div class="flex w-full justify-end">
        <!-- <BaseButtonIcon
          size="sm"
          rounded="full"
          color="warning"

        > -->
        <Icon
          name="mdi:minus"
          class="hover:text-warning-500 size-4"
          @click="all_values.splice(index, 1)"
        />
        <!-- </BaseButtonIcon> -->
      </div>
      <forms-generator

        :schema="inp"
        @updated_form="updated_form($event, index)"
        @submitted_form="submit_form"
      />
    </div>
  </div>
</template>
