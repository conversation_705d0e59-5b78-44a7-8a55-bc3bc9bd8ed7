<script setup lang="ts">
import { useColorMode } from '@vueuse/core'

const props = withDefaults(
  defineProps<{
    /**
     * Sets the toggle element to inverted colors mode.
     */
    inverted?: boolean
    /**
     * Disables transitions when toggling between light and dark mode.
     */
    disableTransitions?: boolean
    item_id?: string
    value?: boolean
  }>(),
  {
    inverted: false,
    disableTransitions: undefined,
    item_id: Math.random().toString(36).substring(7),
  },
)
const disableTransitions = useNuiDefaultProperty(
  props,
  'BaseThemeToggle',
  'disableTransitions',
)
onMounted(() => {
  console.log('props.value HTEME', props.value)
  if (props.value) {
    modelValue.value = props.value
  }
})
const colorMode = useColorMode()
const isDark = computed({
  get() {
    return colorMode.value === 'dark'
  },
  set(value) {
    // disable transitions
    if (process.client && disableTransitions.value) {
      document.documentElement.classList.add('nui-no-transition')
    }
    console.log('value', value)
    console.log('colorMode.value', colorMode.value)
    console.log('colorMode.value', colorMode)
    console.log('colorMode.preference', colorMode.preference)

    colorMode.value = value ? 'dark' : 'light'

    // re-enable transitions
    if (process.client && disableTransitions.value) {
      setTimeout(() => {
        document.documentElement.classList.remove('nui-no-transition')
      }, 0)
    }
  },
})
</script>

<template>
  <label

    class="nui-theme-toggle"
    :class="props.inverted && 'nui-theme-toggle-inverted'"
  >
    <input
      v-model="isDark"
      type="checkbox"
      class="nui-theme-toggle-input"
    >
    <span class="nui-theme-toggle-inner">
      <IconSun class="nui-sun" />
      <IconMoon class="nui-moon" />
    </span>
  </label>
</template>

<style>
.nui-no-transition * {
  transition-property: none !important;
  transition-duration: 0 !important;
}
</style>
