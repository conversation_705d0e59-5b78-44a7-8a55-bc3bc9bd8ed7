<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    icon?: string
    class?: string
    item_id?: string
  }>(),
  {
    icon: 'lucide:circle',
    class: 'h-4 w-4',
    item_id: Math.random().toString(36).substring(7),
  },
)

const classes = computed(() => [props.class])
</script>

<template>
  <client-only>
    <Icon :name="icon ? icon : 'mdi:home'" :class="classes" />
  </client-only>
</template>
