<script setup lang="ts">
  const props: any = withDefaults(
    defineProps<{
      schema: {
        rules: any
        superRefine: any
        fields: {
          label: string
          name: string
          as: string
          type: string
          children?: any
          value?: any
        }[]
      }
    }>(),
    {
      schema: () => {
        return {
          rules: [],
          superRefine: [],
          fields: [],
        }
      },
    }
  )
  import { toTypedSchema } from "@vee-validate/zod"
  import { Field, useForm } from "vee-validate"
  import { z } from "zod"
  const { parseRule } = useForms()
  import { removeUndefined } from "./../../utils/data"

  // This is the Zod schema for the form input
  // It's used to define the shape that the form data will have
  const zodSchema = z
    .object({
      event: z.object(
        props.schema.rules.reduce(
          (
            acc: { [x: string]: z.ZodString },
            rule: { field: string | number; rule: string }
          ) => {
            acc[rule.field] = parseRule(rule.rule)
            return acc
          },
          {} as Record<string, any>
        )
      ),
    })
    .superRefine((data: any, ctx) => {
      for (const rule of props.schema.superRefine) {
        if (
          rule.type === "includes" &&
          data.event[rule.field].includes(data.event[rule.value])
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            path: rule.path,
          })
        } else if (
          rule.type === "!match" &&
          data.event[rule.field] !== data.event[rule.value]
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            path: rule.path,
          })
        } else if (
          rule.type === "match" &&
          data.event[rule.field] === data.event[rule.value]
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: rule.message,
            path: rule.path,
          })
        }
        // Add more conditions here for other rule types
      }
    })

  // Zod has a great infer method that will
  // infer the shape of the schema into a TypeScript type
  type FormInput = z.infer<typeof zodSchema>

  const validationSchema = toTypedSchema(zodSchema)
  const initialValues = computed<FormInput>(() => ({
    event: props.schema.fields.reduce(
      (acc: any, field: { name: any; value: any }) => {
        return {
          ...acc,
          [field.name]: field.value || "",
        }
      },
      {} as FormInput["event"]
    ),
  }))

  const {
    handleSubmit,
    setFieldError,
    isSubmitting,
    meta,
    values,
    errors,
    resetForm,
    setFieldValue,
    setErrors,
  } = useForm({
    validationSchema,
    initialValues,
  })

  const success = ref(false)
  const fieldsWithErrors = computed(() => Object.keys(errors.value).length)

  const toaster = useToaster()
  const em: any = getCurrentInstance()
  // This is where you would send the form data to the server
  const onSubmit = handleSubmit(
    async (values: any) => {
      success.value = false

      // here you have access to the validated form values
      try {
        let data = {}
        if (props.isEditModal) {
        } else {
        }

        let emitData: any = {
          ...values.event,
          ...data,
        }
        em.emit("submit", emitData)
        em.emit("close")
      } catch (error: any) {
        // this will set the error on the form
        if (error.message === "Fake backend validation error") {
          setFieldError("event.title", "This name is not allowed")

          document.documentElement.scrollTo({
            top: 0,
            behavior: "smooth",
          })

          toaster.clearAll()
          toaster.show({
            title: "Oops!",
            message: "Please review the errors in the form",
            color: "danger",
            icon: "lucide:alert-triangle",
            closable: true,
          })
        }
        return
      }

      document.documentElement.scrollTo({
        top: 0,
        behavior: "smooth",
      })

      success.value = true
      setTimeout(() => {
        success.value = false
      }, 3000)
    },
    (error: any) => {
      // this callback is optional and called only if the form has errors
      success.value = false

      // here you have access to the error

      // you can use it to scroll to the first error
      document.documentElement.scrollTo({
        top: 0,
        behavior: "smooth",
      })
    }
  )

  const allow_update = ref(false)
  watch(values.event, (newValue: any, oldValue) => {
    const val = removeUndefined(newValue)
    const hasUndefined = Object.values(val).some((value) => value === undefined)
    if (allow_update.value && !hasUndefined) em.emit("updated_form", newValue)
  })

  const updated_object = (data: any) => {
    let label = data[0]
    let f_data = data[1]
    // see if form as label as object if not update the form
    if (values.event[label] === undefined || values.event[label] === "") {
      setFieldValue(`event.${label}`, f_data)
      return
    }

    // loop over data and update the edit.value.schema
    for (const key in f_data) {
      if (f_data[key] === undefined || f_data[key] === "") continue
      setFieldValue(`event.${label}.${key}`, f_data[key])
    }
  }

  const updated_array = (data: any) => {
    let label = data[0]
    let f_data = data[1]
    // see if form as label as object if not update the form
    if (values.event[label] === undefined || values.event[label] === "") {
      setFieldValue(`event.${label}`, f_data)
      return
    }
    // loop over data and update the edit.value.schema
    for (const key in f_data) {
      if (f_data[key] === undefined || f_data[key] === "") continue
      setFieldValue(`event.${label}.${key}`, f_data[key])
    }
  }

  onMounted(() => {
    // loop over initial values and setFieldValue
    for (const key in initialValues.value.event) {
      if (
        initialValues.value.event[key] === undefined ||
        initialValues.value.event[key] == ""
      )
        continue
      setFieldValue(`event.${key}`, initialValues.value.event[key])
    }

    setTimeout(() => {
      allow_update.value = true
    }, 500)
  })

  const submit_form = () => {
    success.value = false
    // here you have access to the validated form values
    try {
      let data = {}
      // if (props.isEditModal) {
      // }
      // else {
      // }

      let emitData: any = {
        ...values.event,
        ...data,
      }
      em.emit("submitted_form", emitData)
      em.emit("close")
    } catch (error: any) {
      // this will set the error on the form
      if (error.message === "Fake backend validation error") {
        // @ts-expect-error - vee validate typing bug with nested keys
        setFieldError("event.title", "This name is not allowed")

        document.documentElement.scrollTo({
          top: 0,
          behavior: "smooth",
        })
      }
      return
    }

    // resetForm()

    document.documentElement.scrollTo({
      top: 0,
      behavior: "smooth",
    })

    success.value = true
    setTimeout(() => {
      success.value = false
    }, 3000)
    onSubmit()
  }
</script>

<template>
  <form action="" method="POST" @submit.prevent="onSubmit">
    <div>
      <slot name="form_top" :on-button-click="submit_form" />
    </div>
    <div class="grid grid-cols-12 gap-2">
      <div
        v-for="(inp, index) in schema.fields"
        :key="index"
        :class="inp.class"
      >
        <Field
          v-slot="{ field, errorMessage, handleChange, handleBlur }"
          :name="`event.${inp.name}`"
        >
          <component
            :is="inp.component"
            :keys="inp.keys"
            :value="inp.value"
            :label="inp.label"
            :shape="inp.shape"
            :icon="inp.icon"
             :include="inp.include || []"
            :exclude="inp.exclude || []"
            :placeholder="inp.placeholder"
            :disabled="isSubmitting"
            :classes="{
              input: '!h-11 !ps-11',
              icon: '!h-11 !w-11',
            }"
            :model-value="field.value"
            :error="errorMessage"
            :type="inp.type"
            :name="inp.name"
            :collection="inp.collection"
            :options="inp.options"
            :multi="inp.multi || false"
                        @update:model-value="handleChange"
            @blur="handleBlur"
            @object-updated="updated_object"
            @array-updated="updated_array"
          >
            <option
              v-for="(option, index) in inp.options"
              v-if="
                inp.component === 'BaseSelect' ||
                inp.component === 'BaseSelectMulti'
              "
              :key="index"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </component>
        </Field>
      </div>
    </div>

    <div>
      <slot name="form_bottom" :on-button-click="submit_form" />
    </div>
    <form-save @submit="submit_form" />
  </form>
</template>
: { event: { [x: string]: any; }; }: { addIssue: (arg0: { code: any; message:
any; path: any; }) => void; }
