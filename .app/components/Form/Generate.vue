<script setup lang="ts">
const props = defineProps({
  filter: {
    type: String,
    default: "shippingClass",
  },
})

  const emits = defineEmits<{
    updated_form: [props: any]
  }>()

  const { close } = usePanels()
  onKeyStroke("Escape", close)


  const { data, pending, error, refresh }: any = await useFetch(
    "/api/forms/data",
    {
      query: {
        filter: props.filter
      },
    }
  )

  let formData: any = ref({})

  const retrieveData = computed(() => {
    return data.value?.data?.[0] || {}
  })

  onMounted(() => {
    setTimeout(() => {
      console.log("retrieveData", retrieveData.value)
      formData.value = data.value?.data?.[0] || {}
    }, 500)
  })

  const onSubmit = async () => {
   
  }


</script>

<template>
  <FormGenerator
    :schema="formData.schema"
    @submitted_form="onSubmit"
  >
    <template #form_bottom>
      <BaseButton type="submit" @click="onSubmit" class="w-full bg-primary-500">
        Create
      </BaseButton>
    </template>
  </FormGenerator>
</template>
