// export default defineEventHandler(async (event) => {
//   const query = getQuery(event)
//   const perPage = parseInt((query.perPage as string) || "5", 10)
//   const page = parseInt((query.page as string) || "1", 10)
//   const filter = (query.filter as string) || ""

//   if (perPage >= 200) {
//     // Create an artificial delay
//     await new Promise((resolve) => setTimeout(resolve, 2000))
//   }

//   const data = await getData()

//   return {
//     total: data.length,
//     data: filterData(data, filter, page, perPage),
//   }
// })

// function filterData(
//   data: any[],
//   filter: string,
//   page: number,
//   perPage: number
// ) {
//   const offset = (page - 1) * perPage
//   if (!filter) {
//     return data.slice(offset, offset + perPage)
//   }
//   const filterRe = new RegExp(filter, "i")
//   return data
//     .filter((item) => {
//       return [item.title, item.slug].some((item) => item.match(filterRe))
//     })
//     .slice(offset, offset + perPage)
// }

// async function getData() {
//   return Promise.resolve([
//     {
//       id: 1,
//       title: "Agent",
//       subtitle: "Agents to your agency",
//       purpose: "Add a new agent to your agency",
//       slug: "agent",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Type",
//             name: "type",
//             as: "select",
//             placeholder: "Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [],
//           },
//           {
//             label: "Avatar",
//             name: "avatar",
//             as: "input",
//             type: "text",
//             placeholder: "Avatar",
//             component: "BaseUploadAvatar",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Description",
//             name: "description",
//             as: "input",
//             type: "text",
//             placeholder: "Description",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Instructions",
//             name: "instructions",
//             as: "input",
//             type: "text",
//             placeholder: "Instructions",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Agency",
//             name: "agency",
//             as: "input",
//             type: "all",
//             placeholder: "Agency",
//             component: "BaseSelectAgency",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             multi: true,
//             class: "col-span-12",
//           },
//           {
//             label: "LLM",
//             name: "llm",
//             as: "input",
//             type: "llm",
//             include: ["llm"],
//             exclude: [],
//             placeholder: "LLM",
//             component: "BaseSelectIntegrations",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },

//           {
//             label: "Tools",
//             name: "tools",
//             as: "input",
//             type: "all",
//             include: [],
//             exclude: ["llm"],
//             placeholder: "Tools",
//             component: "BaseSelectIntegrations",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             multi: true,
//             class: "col-span-12",
//           },
//           {
//             label: "Files",
//             name: "uploads",
//             as: "input",
//             type: "llm",
//             placeholder: "Files",
//             component: "BaseUpload",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             multi: true,
//             class: "col-span-12",
//           },
//           {
//             label: "Status",
//             name: "status",
//             as: "select",
//             placeholder: "Status",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [
//               {
//                 value: "active",
//                 label: "Active",
//               },
//               {
//                 value: "inactive",
//                 label: "Inactive",
//               },
//             ],
//           },
//         ],
//       },
//     },
//     {
//       id: 2,
//       title: "Category",
//       subtitle: "Categories for your products",
//       purpose: "Add a new category for your products",
//       slug: "category",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "Category Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Slug",
//             name: "slug",
//             as: "input",
//             type: "text",
//             placeholder: "category-slug",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Parent Category",
//             name: "parentId",
//             as: "select",
//             placeholder: "Select Parent Category",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//             options: [], // This should be populated with existing categories
//           },
//           {
//             label: "Description",
//             name: "description",
//             as: "input",
//             type: "text",
//             placeholder: "Category Description",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Display Type",
//             name: "display",
//             as: "select",
//             placeholder: "Select Display Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [
//               { value: "default", label: "Default" },
//               { value: "products", label: "Products" },
//               { value: "subcategories", label: "Subcategories" },
//               { value: "both", label: "Both" },
//             ],
//           },
//           {
//             label: "Image",
//             name: "image",
//             as: "input",
//             type: "file",
//             placeholder: "Category Image",
//             component: "BaseUpload",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//             accept: "image/*",
//           },
//           {
//             label: "Menu Order",
//             name: "menuOrder",
//             as: "input",
//             type: "number",
//             placeholder: "Menu Order",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//         ],
//       },
//     },
//     {
//       id: 3,
//       title: "Product",
//       subtitle: "Products in your store",
//       purpose: "Add a new product to your store",
//       slug: "product",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "Product Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Slug",
//             name: "slug",
//             as: "input",
//             type: "text",
//             placeholder: "product-slug",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Type",
//             name: "type",
//             as: "select",
//             placeholder: "Select Product Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [
//               { value: "simple", label: "Simple" },
//               { value: "grouped", label: "Grouped" },
//               { value: "external", label: "External" },
//               { value: "variable", label: "Variable" },
//             ],
//           },
//           {
//             label: "Description",
//             name: "description",
//             as: "input",
//             type: "text",
//             placeholder: "Product Description",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Short Description",
//             name: "shortDescription",
//             as: "input",
//             type: "text",
//             placeholder: "Short Description",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Regular Price",
//             name: "regularPrice",
//             as: "input",
//             type: "number",
//             placeholder: "Regular Price",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Sale Price",
//             name: "salePrice",
//             as: "input",
//             type: "number",
//             placeholder: "Sale Price",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "SKU",
//             name: "sku",
//             as: "input",
//             type: "text",
//             placeholder: "SKU",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Manage Stock",
//             name: "manageStock",
//             as: "checkbox",
//             component: "BaseCheckbox",
//             shape: "curved",
//             icon: "",
//             value: false,
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Stock Quantity",
//             name: "stock",
//             as: "input",
//             type: "number",
//             placeholder: "Stock Quantity",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//             conditionalDisplay: { field: "manageStock", value: true },
//           },
//           {
//             label: "Categories",
//             name: "categories",
//             as: "select",
//             placeholder: "Select Categories",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: [],
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//             options: [], // This should be populated with existing categories
//             multi: true,
//           },
//           {
//             label: "Tags",
//             name: "tags",
//             as: "select",
//             placeholder: "Select Tags",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: [],
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//             options: [], // This should be populated with existing tags
//             multi: true,
//           },
//           {
//             label: "Images",
//             name: "images",
//             as: "input",
//             type: "file",
//             placeholder: "Product Images",
//             component: "BaseUpload",
//             shape: "curved",
//             icon: "",
//             value: [],
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//             accept: "image/*",
//             multi: true,
//           },
//           {
//             label: "Status",
//             name: "status",
//             as: "select",
//             placeholder: "Select Status",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [
//               { value: "draft", label: "Draft" },
//               { value: "pending", label: "Pending Review" },
//               { value: "private", label: "Private" },
//               { value: "publish", label: "Published" },
//             ],
//           },
//         ],
//       },
//     },
//     {
//       id: 4,
//       title: "Order",
//       subtitle: "Orders in your store",
//       purpose: "Add a new order to your store",
//       slug: "order",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Status",
//             name: "status",
//             as: "select",
//             placeholder: "Select Order Status",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [
//               { value: "pending", label: "Pending Payment" },
//               { value: "processing", label: "Processing" },
//               { value: "on-hold", label: "On Hold" },
//               { value: "completed", label: "Completed" },
//               { value: "cancelled", label: "Cancelled" },
//               { value: "refunded", label: "Refunded" },
//               { value: "failed", label: "Failed" },
//             ],
//           },
//           {
//             label: "Customer",
//             name: "customerId",
//             as: "select",
//             placeholder: "Select Customer",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [], // This should be populated with existing customers
//           },
//           {
//             label: "Billing First Name",
//             name: "billing.firstName",
//             as: "input",
//             type: "text",
//             placeholder: "Billing First Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Billing Last Name",
//             name: "billing.lastName",
//             as: "input",
//             type: "text",
//             placeholder: "Billing Last Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Billing Address",
//             name: "billing.address1",
//             as: "input",
//             type: "text",
//             placeholder: "Billing Address",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Billing City",
//             name: "billing.city",
//             as: "input",
//             type: "text",
//             placeholder: "Billing City",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Billing State",
//             name: "billing.state",
//             as: "input",
//             type: "text",
//             placeholder: "Billing State",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Billing Postcode",
//             name: "billing.postcode",
//             as: "input",
//             type: "text",
//             placeholder: "Billing Postcode",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Billing Country",
//             name: "billing.country",
//             as: "select",
//             placeholder: "Select Billing Country",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//             options: [], // This should be populated with country list
//           },
//           {
//             label: "Billing Email",
//             name: "billing.email",
//             as: "input",
//             type: "email",
//             placeholder: "Billing Email",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Billing Phone",
//             name: "billing.phone",
//             as: "input",
//             type: "tel",
//             placeholder: "Billing Phone",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Ship to different address",
//             name: "shipToDifferentAddress",
//             as: "checkbox",
//             component: "BaseCheckbox",
//             shape: "curved",
//             icon: "",
//             value: false,
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//           // Shipping fields (similar to billing fields)
//           // These fields should be conditionally displayed based on the "Ship to different address" checkbox
//           {
//             label: "Payment Method",
//             name: "paymentMethod",
//             as: "select",
//             placeholder: "Select Payment Method",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [], // This should be populated with available payment methods
//           },
//           {
//             label: "Order Notes",
//             name: "customerNote",
//             as: "input",
//             type: "text",
//             placeholder: "Order Notes",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//         ],
//       },
//     },
//     {
//       id: 5,
//       title: "Shipping Class",
//       subtitle: "Shipping classes for your products",
//       purpose: "Add a new shipping class for your products",
//       slug: "shippingClass",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "Shipping Class Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Estimated Shipping Time",
//             name: "estimatedShippingTime",
//             as: "input",
//             type: "text",
//             placeholder: "e.g., 3-5 business days",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Estimated Shipping Time Type",
//             name: "estimatedShippingTimeType",
//             as: "select",
//             placeholder: "Select Time Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//             options: [
//               { value: "minutes", label: "Minutes" },
//               { value: "hours", label: "Hours" },
//               { value: "days", label: "Days" },
//             ],
//           },
//           {
//             label: "Estimated Shipping Value",
//             name: "estimatedShippingValue",
//             as: "input",
//             type: "number",
//             placeholder: "Enter value",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Can Be Shipped Separately",
//             name: "canBeShippedSeparately",
//             as: "checkbox",
//             component: "BaseCheckbox",
//             shape: "curved",
//             icon: "",
//             value: false,
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Suitable For Delivery",
//             name: "suitableForDelivery",
//             as: "checkbox",
//             component: "BaseCheckbox",
//             shape: "curved",
//             icon: "",
//             value: true,
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Shipping Notes",
//             name: "shippingNotes",
//             as: "input",
//             type: "text",
//             placeholder: "Additional shipping information",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Estimated Shipping Cost",
//             name: "estimatedShippingCost",
//             as: "input",
//             type: "number",
//             placeholder: "Estimated cost",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Shipping Distance Type",
//             name: "shippingDistanceType",
//             as: "select",
//             placeholder: "Select Distance Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//             options: [
//               { value: "local", label: "Local" },
//               { value: "regional", label: "Regional" },
//               { value: "national", label: "National" },
//               { value: "international", label: "International" },
//             ],
//           },
//           {
//             label: "Shipping Distance Value",
//             name: "shippingDistanceValue",
//             as: "input",
//             type: "number",
//             placeholder: "Enter distance value",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Shipping Distance Unit",
//             name: "shippingDistanceUnit",
//             as: "select",
//             placeholder: "Select Distance Unit",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//             options: [
//               { value: "km", label: "Kilometers" },
//               { value: "mi", label: "Miles" },
//             ],
//           },
//         ],
//       },
//     },
//     {
//       id: 6,
//       title: "Product Attribute",
//       subtitle: "Attributes for your products",
//       purpose: "Add a new attribute for your products",
//       slug: "product-attribute",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "Attribute Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Slug",
//             name: "slug",
//             as: "input",
//             type: "text",
//             placeholder: "attribute-slug",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Type",
//             name: "type",
//             as: "select",
//             placeholder: "Select Attribute Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [
//               { value: "select", label: "Select" },
//               { value: "text", label: "Text" },
//             ],
//           },
//           {
//             label: "Order By",
//             name: "orderBy",
//             as: "select",
//             placeholder: "Select Order By",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//             options: [
//               { value: "menu_order", label: "Menu Order" },
//               { value: "name", label: "Name" },
//               { value: "name_num", label: "Name (numeric)" },
//               { value: "id", label: "ID" },
//             ],
//           },
//           {
//             label: "Has Archives",
//             name: "hasArchives",
//             as: "checkbox",
//             component: "BaseCheckbox",
//             shape: "curved",
//             icon: "",
//             value: false,
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//         ],
//       },
//     },
//     {
//       id: 7,
//       title: "Product Attribute Term",
//       subtitle: "Terms for your product attributes",
//       purpose: "Add a new term for an attribute",
//       slug: "product-attribute-term",
//       schema: {
//         fields: [
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "Term Name",
//             component: "BaseInput",
//             required: true,
//             class: "col-span-12",
//           },
//           {
//             label: "Slug",
//             name: "slug",
//             as: "input",
//             type: "text",
//             placeholder: "term-slug",
//             component: "BaseInput",
//             required: true,
//             class: "col-span-12",
//           },
//           {
//             label: "Description",
//             name: "description",
//             as: "input",
//             type: "text",
//             placeholder: "Term Description",
//             component: "BaseTextarea",
//             required: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Menu Order",
//             name: "menuOrder",
//             as: "input",
//             type: "number",
//             placeholder: "Menu Order",
//             component: "BaseInput",
//             required: false,
//             class: "col-span-12",
//           },
//         ],
//       },
//     },

//     {
//       id: 3,
//       title: "Product Variation",
//       subtitle: "Variations for your products",
//       purpose: "Add a new variation for your product",
//       slug: "product-variation",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "SKU",
//             name: "sku",
//             as: "input",
//             type: "text",
//             placeholder: "Variation SKU",
//             component: "BaseInput",
//             required: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Price",
//             name: "price",
//             as: "input",
//             type: "number",
//             placeholder: "Price",
//             component: "BaseInput",
//             required: true,
//             class: "col-span-12",
//           },
//           // ... add other fields as necessary ...
//           {
//             label: "Attributes",
//             name: "attributes",
//             as: "multi-select",
//             placeholder: "Select Attributes",
//             component: "BaseMultiSelect",
//             options: [], // Populate dynamically
//             required: true,
//             class: "col-span-12",
//           },
//         ],
//       },
//     },
//     {
//       id: 3,
//       title: "Tag",
//       subtitle: "Tags for your products",
//       purpose: "Add a new tag for your products",
//       slug: "tag",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "Tag Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Slug",
//             name: "slug",
//             as: "input",
//             type: "text",
//             placeholder: "tag-slug",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Description",
//             name: "description",
//             as: "input",
//             type: "text",
//             placeholder: "Tag Description",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//         ],
//       },
//     },
//     {
//       id: 3,
//       title: "Account",
//       subtitle: "Create or edit an account",
//       purpose: "Add a new account or edit an existing one",
//       slug: "account",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "Account Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Description",
//             name: "description",
//             as: "textarea",
//             placeholder: "Account Description",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Type",
//             name: "type",
//             as: "select",
//             placeholder: "Select Account Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//             options: [
//               { value: "customer", label: "Customer" },
//               { value: "supplier", label: "Supplier" },
//               { value: "partner", label: "Partner" },
//             ],
//           },
//           {
//             label: "Avatar",
//             name: "avatar",
//             as: "input",
//             type: "file",
//             placeholder: "Account Avatar",
//             component: "BaseUpload",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//             accept: "image/*",
//           },
//         ],
//       },
//     },
//     {
//       id: 4,
//       title: "Contact",
//       subtitle: "Create or edit a contact",
//       purpose: "Add a new contact or edit an existing one",
//       slug: "contact",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Username",
//             name: "username",
//             as: "input",
//             type: "text",
//             placeholder: "Username",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Name",
//             name: "name",
//             as: "input",
//             type: "text",
//             placeholder: "First Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Surname",
//             name: "surname",
//             as: "input",
//             type: "text",
//             placeholder: "Last Name",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Email",
//             name: "email",
//             as: "input",
//             type: "email",
//             placeholder: "Email Address",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Bio",
//             name: "bio",
//             as: "textarea",
//             placeholder: "Bio",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Short Bio",
//             name: "shortBio",
//             as: "textarea",
//             placeholder: "Short Bio",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-12",
//           },
//         ],
//       },
//     },
//     {
//       id: 1,
//       title: "Address",
//       subtitle: "Create or edit an address",
//       purpose: "Add a new address or edit an existing one",
//       slug: "address",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Street",
//             name: "street",
//             as: "input",
//             type: "text",
//             placeholder: "Street",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "City",
//             name: "city",
//             as: "input",
//             type: "text",
//             placeholder: "City",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "State",
//             name: "state",
//             as: "input",
//             type: "text",
//             placeholder: "State",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Postal Code",
//             name: "postalCode",
//             as: "input",
//             type: "text",
//             placeholder: "Postal Code",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Country",
//             name: "country",
//             as: "select",
//             placeholder: "Select Country",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//             options: [
//               { value: "usa", label: "United States" },
//               { value: "canada", label: "Canada" },
//               // Add more countries as needed
//             ],
//           },
//           {
//             label: "Latitude",
//             name: "latitude",
//             as: "input",
//             type: "number",
//             placeholder: "Latitude",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Longitude",
//             name: "longitude",
//             as: "input",
//             type: "number",
//             placeholder: "Longitude",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//           {
//             label: "Formatted Address",
//             name: "formattedAddress",
//             as: "input",
//             type: "text",
//             placeholder: "Formatted Address",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: false,
//             disabled: true, // Assuming this is auto-generated
//             class: "col-span-12",
//           },
//           {
//             label: "Address Type",
//             name: "addressType",
//             as: "select",
//             placeholder: "Select Address Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//             options: [
//               { value: "home", label: "Home" },
//               { value: "work", label: "Work" },
//               { value: "billing", label: "Billing" },
//               { value: "shipping", label: "Shipping" },
//             ],
//           },
//           {
//             label: "Is Primary",
//             name: "isPrimary",
//             as: "checkbox",
//             component: "BaseCheckbox",
//             shape: "curved",
//             icon: "",
//             value: false,
//             required: false,
//             disabled: false,
//             class: "col-span-6",
//           },
//         ],
//       },
//     },
//     {
//       id: 3,
//       title: "Notification",
//       subtitle: "Create or edit a notification",
//       purpose: "Add a new notification or edit an existing one",
//       slug: "notification",
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: "Title",
//             name: "title",
//             as: "input",
//             type: "text",
//             placeholder: "Notification Title",
//             component: "BaseInput",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Content",
//             name: "content",
//             as: "textarea",
//             placeholder: "Notification Content",
//             component: "BaseTextarea",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-12",
//           },
//           {
//             label: "Type",
//             name: "type",
//             as: "select",
//             placeholder: "Select Type",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//             options: [
//               { value: "info", label: "Info" },
//               { value: "warning", label: "Warning" },
//               { value: "error", label: "Error" },
//               { value: "success", label: "Success" },
//             ],
//           },
//           {
//             label: "Priority",
//             name: "priority",
//             as: "select",
//             placeholder: "Select Priority",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//             options: [
//               { value: 1, label: "Low" },
//               { value: 2, label: "Medium" },
//               { value: 3, label: "High" },
//             ],
//           },
//           {
//             label: "Status",
//             name: "status",
//             as: "select",
//             placeholder: "Select Status",
//             component: "BaseSelect",
//             shape: "curved",
//             icon: "",
//             value: "",
//             required: true,
//             disabled: false,
//             class: "col-span-6",
//             options: [
//               { value: "unread", label: "Unread" },
//               { value: "read", label: "Read" },
//               { value: "archived", label: "Archived" },
//             ],
//           },
//         ],
//       },
//     },
//     {
//       id: 'product-branding',
//       title: 'Product Branding',
//       subtitle: 'Customize the branding for your product',
//       purpose: 'Create or update product branding',
//       slug: 'product-branding',
//       schema: {
//         rules: [],
//         superRefine: [],
//         fields: [
//           {
//             label: 'Logo',
//             name: 'logo',
//             as: 'input',
//             type: 'file',
//             placeholder: 'Upload logo',
//             component: 'BaseUpload',
//             shape: 'curved',
//             icon: '',
//             value: '',
//             required: false,
//             disabled: false,
//             class: 'col-span-12',
//             accept: 'image/*',
//           },
//           {
//             label: 'Primary Color',
//             name: 'primaryColor',
//             as: 'input',
//             type: 'color',
//             placeholder: 'Select primary color',
//             component: 'BaseInput',
//             shape: 'curved',
//             icon: '',
//             value: '',
//             required: false,
//             disabled: false,
//             class: 'col-span-6',
//           },
//           {
//             label: 'Secondary Color',
//             name: 'secondaryColor',
//             as: 'input',
//             type: 'color',
//             placeholder: 'Select secondary color',
//             component: 'BaseInput',
//             shape: 'curved',
//             icon: '',
//             value: '',
//             required: false,
//             disabled: false,
//             class: 'col-span-6',
//           },
//           {
//             label: 'Font Family',
//             name: 'fontFamily',
//             as: 'select',
//             placeholder: 'Select font family',
//             component: 'BaseSelect',
//             shape: 'curved',
//             icon: '',
//             value: '',
//             required: false,
//             disabled: false,
//             class: 'col-span-12',
//             options: [
//               { value: 'Arial', label: 'Arial' },
//               { value: 'Helvetica', label: 'Helvetica' },
//               { value: 'Times New Roman', label: 'Times New Roman' },
//               { value: 'Georgia', label: 'Georgia' },
//               { value: 'Verdana', label: 'Verdana' },
//               { value: 'Courier', label: 'Courier' },
//             ],
//           },
//           {
//             label: 'Custom Text',
//             name: 'customText',
//             as: 'textarea',
//             placeholder: 'Enter custom text for branding',
//             component: 'BaseTextarea',
//             shape: 'curved',
//             icon: '',
//             value: '',
//             required: false,
//             disabled: false,
//             class: 'col-span-12',
//           },
//         ],
//       },
//     },
//   ]
//   )
// }
