connectorId: turbois
generate:
  javascriptSdk:
    # outputDir: ./../.dataconnect/generated/default-connector/javascript-sdk
    # package: "@firebasegen/default-connector"
    # Create a custom package name for your generated SDK
    package: turbois-db
    # Tells Data Connect where to store the generated SDK code, this should be in the same
    # directory as your app code
    outputDir: ../../db/dataconnect-generated/javascript-sdk
  kotlinSdk:
    outputDir: ../../db/dataconnect-generated/kotlin-sdk
    package: com.google.firebase.dataconnect.connectors.default-connector
  swiftSdk: null
