specVersion: v1
serviceId: pib
location: us-central1
schema:
  source: ./schema
  datasource:
    postgresql:
      database: fdcdb
      cloudSql:
        instanceId: pib-fdc
        # schemaValidation: "STRICT"     # STRICT mode makes Postgres schema match Data Connect exactly.
        # schemaValidation: "COMPATIBLE" # COMPATIBLE mode makes Postgres schema compatible with Data Connect.
connectorDirs: [./actions]
generate:
  javascriptSdk: {}
  kotlinSdk: {}
  swiftSdk: null
