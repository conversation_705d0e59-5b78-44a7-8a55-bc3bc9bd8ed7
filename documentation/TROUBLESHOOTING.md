# Troubleshooting

## Nuxt dev fails with `spawn <PERSON>BADF`

On macOS with Node 22 and Nuxt 4, the Nuxt CLI may fail when running in forked mode (default) with an error like:

```
[unhandledRejection] spawn <PERSON><PERSON>DF
```

Fix: disable forked mode for the dev server.

- We set `.app/package.json` dev script to:

  ````
  nuxt dev --no-fork
  ````

- Alternatively, run explicitly:

  ```sh
  pnpm --filter=app exec nuxt dev --no-fork
  ```

This avoids the child_process spawn/fork error and runs the server in the same process.

## Router error: `Missing required param "folder"`

If you navigate to `/wizard` while routes were defined under `pages/[folder]/wizard`, Vue Router required the `folder` param and throws this error.

Fix: make `folder` optional by moving the wizard pages under `pages/[[folder]]/wizard`.

Now `/wizard` works (no param), and `/myFolder/wizard` also works (with param).
