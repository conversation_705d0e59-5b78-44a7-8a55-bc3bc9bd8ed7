{"chat.agent.enabled": true, "chat.agent.maxRequests": 15, "github.copilot.chat.agent.runTasks": true, "chat.mcp.discovery.enabled": {"claude-desktop": true, "windsurf": true, "cursor-global": true, "cursor-workspace": true}, "github.copilot.chat.agent.autoFix": true, "chat.tools.autoApprove": false, "editor.formatOnSave": false, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": {"source.fixAll.eslint": "never"}, "[markdown]": {"editor.rulers": [80]}, "eslint.validate": ["javascript", "javascriptreact", "markdown", "typescript", "vue"], "tailwindCSS.experimental.configFile": ".demo/tailwind.config.ts", "typescript.tsdk": "node_modules/typescript/lib", "prettier.enable": false, "vue.codeActions.enabled": false}