{"version": "2.0.0", "tasks": [{"label": "Create documentation directory", "type": "shell", "command": "mkdir -p /Users/<USER>/Projects/own/turbois/documentation && echo 'Creating PRD documentation directory'"}, {"label": "Create comprehensive PRD document", "type": "shell", "command": "cat > /Users/<USER>/Projects/own/turbois/documentation/Turbois-System-PRD.md << 'EOF'\n# Turbois System - Product Requirements Document\n\n## Executive Summary\n\nTurbois is a comprehensive workspace and CRM management platform that enables users to create and manage multiple workspaces with integrated CRM functionality. The system supports personal, community, and company workspaces with seamless cross-workspace data sharing and profile linkages.\n\n## System Overview\n\n### Core Architecture\n\nTurbois implements a multi-tenant workspace system where:\n- **Users** can create and join multiple workspaces\n- **Workspaces** can be personal, community, or company types\n- **Profiles** are attached to workspaces and can be shared across them\n- **CRM functionality** is integrated at the workspace level\n- **Addresses** can be shared across all entities\n\n### Key Differentiators\n\n1. **Flexible Workspace Model**: Users can maintain separate contexts for different aspects of their work/life\n2. **Cross-Workspace Linking**: Companies and contacts can be linked across workspaces for collaboration\n3. **Profile-Based Connections**: CRM contacts link to user profiles for unified customer management\n4. **Automatic Data Propagation**: Financial documents automatically appear in linked workspaces\n\n## User Journey\n\n### 1. User Registration & Onboarding\n- User signs up with email/username\n- Creates initial personal workspace\n- Attaches a profile to the workspace\n\n### 2. Workspace Management\n- Create additional workspaces (personal/community/company)\n- Choose to create new profile or use existing one\n- Invite members to community/company workspaces\n\n### 3. CRM Operations\n- Create companies within workspaces\n- Link companies to other workspaces for shared access\n- Add contacts to companies\n- Link contacts to profiles across the system\n- Generate invoices and quotes that auto-propagate to linked workspaces\n\n### 4. Data Sharing & Collaboration\n- Addresses shared across users, profiles, workspaces\n- Financial documents automatically visible in linked workspaces\n- Profile-linked contacts provide unified customer views\n\n## Technical Architecture\n\n### Data Model\n\n#### Core Entities\n\n**SystemUser**\n- Authentication and user management\n- Can own multiple workspaces and profiles\n- Has personal addresses\n\n**Workspace**\n- Container for CRM data and collaboration\n- Types: personal, community, company\n- Contains companies, contacts, financial documents\n- Can be linked to other workspaces\n\n**Profile**\n- User personas attached to workspaces\n- Can be shared across multiple workspaces\n- Linked to CRM contacts for unified customer management\n\n**Address**\n- Reusable address entity\n- Can be shared across users, profiles, workspaces\n- Supports multiple address types (home, work, billing, shipping)\n\n#### CRM Entities\n\n**Company**\n- CRM company records within workspaces\n- Can be linked to other workspaces for shared access\n- Contains contacts, invoices, quotes\n\n**CRMContact**\n- Contact records within workspaces\n- Can be linked to profiles across the system\n- Associated with companies\n\n**Invoice & Quote**\n- Financial documents tied to companies\n- Automatically shared with linked workspaces\n- Track payment/collection status\n\n### Data Relationships\n\n```\nSystemUser\n├── workspaces (1:many)\n├── addresses (1:many)\n└── profiles (1:many)\n\nWorkspace\n├── profile (1:1)\n├── companies (1:many)\n├── crmContacts (1:many)\n├── invoices (1:many - accessible)\n├── quotes (1:many - accessible)\n└── addresses (1:many)\n\nProfile\n├── workspaces (many:1)\n├── crmContacts (1:many - linked)\n└── addresses (1:many)\n\nCompany\n├── workspace (many:1 - owner)\n├── linkedWorkspace (1:1 - optional)\n├── contacts (1:many)\n├── invoices (1:many)\n├── quotes (1:many)\n└── addresses (1:many)\n\nCRMContact\n├── workspace (many:1 - owner)\n├── linkedProfile (1:1 - optional)\n├── company (many:1 - optional)\n└── addresses (1:many)\n\nInvoice/Quote\n├── company (many:1)\n└── [auto-shared with linked workspace]\n\nAddress\n└── [shared across all entities]\n```\n\n## Features and Requirements\n\n### Core Features\n\n#### 1. User Management\n- **REQ-USER-001**: User registration with email/username validation\n- **REQ-USER-002**: Profile creation and management\n- **REQ-USER-003**: Multiple workspace support per user\n- **REQ-USER-004**: Address management with sharing capabilities\n\n#### 2. Workspace Management\n- **REQ-WS-001**: Create personal/community/company workspaces\n- **REQ-WS-002**: Workspace profile attachment (new or existing)\n- **REQ-WS-003**: Member invitation and management for community/company workspaces\n- **REQ-WS-004**: Workspace-specific address management\n\n#### 3. CRM Functionality\n- **REQ-CRM-001**: Company creation within workspaces\n- **REQ-CRM-002**: Cross-workspace company linking\n- **REQ-CRM-003**: Contact management within workspaces\n- **REQ-CRM-004**: Cross-system contact-to-profile linking\n- **REQ-CRM-005**: Invoice generation with auto-propagation\n- **REQ-CRM-006**: Quote management with workspace sharing\n\n#### 4. Data Sharing & Integration\n- **REQ-DATA-001**: Address sharing across entities\n- **REQ-DATA-002**: Automatic financial document propagation\n- **REQ-DATA-003**: Profile-based contact unification\n- **REQ-DATA-004**: Workspace collaboration features\n\n### Technical Requirements\n\n#### Performance\n- **REQ-TECH-001**: Sub-second response times for CRM operations\n- **REQ-TECH-002**: Support for 1000+ concurrent users\n- **REQ-TECH-003**: Efficient cross-workspace data queries\n\n#### Security\n- **REQ-SEC-001**: Workspace-level data isolation\n- **REQ-SEC-002**: Granular permission system\n- **REQ-SEC-003**: Secure cross-workspace data sharing\n- **REQ-SEC-004**: Audit logging for all operations\n\n#### Scalability\n- **REQ-SCALE-001**: Horizontal scaling support\n- **REQ-SCALE-002**: Database optimization for complex relationships\n- **REQ-SCALE-003**: CDN integration for static assets\n\n## Integration Points\n\n### External Systems\n\n#### Payment Processing\n- Integration with Stripe/PayPal for invoice payments\n- Webhook handling for payment status updates\n- Automated reconciliation with financial documents\n\n#### Email & Communication\n- SMTP integration for invoice/quote delivery\n- Email templates for CRM communications\n- Notification system for workspace updates\n\n#### Calendar Integration\n- Google Calendar/Outlook integration\n- Meeting scheduling for CRM activities\n- Reminder system for due dates\n\n### API Design\n\n#### RESTful Endpoints\n- `/api/users` - User management\n- `/api/workspaces` - Workspace operations\n- `/api/profiles` - Profile management\n- `/api/crm/companies` - Company CRUD\n- `/api/crm/contacts` - Contact management\n- `/api/crm/invoices` - Invoice operations\n- `/api/crm/quotes` - Quote management\n\n#### WebSocket Support\n- Real-time updates for workspace collaboration\n- Live notifications for shared document changes\n- Instant messaging within workspaces\n\n## Success Metrics\n\n### User Engagement\n- **METRIC-USER-001**: Daily/Weekly active users\n- **METRIC-USER-002**: Average workspaces per user\n- **METRIC-USER-003**: CRM adoption rate\n- **METRIC-USER-004**: Cross-workspace linking usage\n\n### Business Impact\n- **METRIC-BIZ-001**: Invoice processing efficiency\n- **METRIC-BIZ-002**: Customer data unification rate\n- **METRIC-BIZ-003**: Time to create/manage CRM records\n- **METRIC-BIZ-004**: User retention and churn rates\n\n### Technical Performance\n- **METRIC-TECH-001**: System uptime (99.9% target)\n- **METRIC-TECH-002**: Average response times\n- **METRIC-TECH-003**: Error rates and incident frequency\n- **METRIC-TECH-004**: Database query performance\n\n## Implementation Roadmap\n\n### Phase 1: Core Platform (Months 1-3)\n- User authentication and registration\n- Basic workspace creation and management\n- Profile system implementation\n- Address sharing functionality\n\n### Phase 2: CRM Foundation (Months 4-6)\n- Company and contact management\n- Basic invoice/quote functionality\n- Workspace-level CRM operations\n- Initial cross-workspace linking\n\n### Phase 3: Advanced Features (Months 7-9)\n- Advanced sharing and collaboration features\n- Integration with external systems\n- Performance optimization\n- Mobile application development\n\n### Phase 4: Enterprise Features (Months 10-12)\n- Advanced permission systems\n- Audit logging and compliance\n- Enterprise integrations\n- Advanced analytics and reporting\n\n## Risk Assessment\n\n### Technical Risks\n- **RISK-TECH-001**: Complex data relationships may impact performance\n- **RISK-TECH-002**: Cross-workspace data consistency challenges\n- **RISK-TECH-003**: Scalability concerns with shared data model\n\n### Business Risks\n- **RISK-BIZ-001**: User adoption of multi-workspace concept\n- **RISK-BIZ-002**: Competition from established CRM platforms\n- **RISK-BIZ-003**: Data privacy and compliance requirements\n\n### Mitigation Strategies\n- **MITIGATION-001**: Comprehensive performance testing and optimization\n- **MITIGATION-002**: Phased rollout with user feedback integration\n- **MITIGATION-003**: Strong data governance and security measures\n- **MITIGATION-004**: Competitive differentiation through unique sharing model\n\n---\n\n*This PRD is based on the comprehensive type system and architectural discussions for the Turbois workspace and CRM platform.*\nEOF"}]}