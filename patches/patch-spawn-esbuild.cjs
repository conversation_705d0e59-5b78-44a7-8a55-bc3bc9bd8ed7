// Patch child_process to avoid using 'inherit' for esbuild stderr, which can cause EBADF
// in some terminal environments. We replace stderr 'inherit' with 'pipe' and forward it.
// This file is intended to be preloaded via NODE_OPTIONS=--require=./patches/patch-spawn-esbuild.cjs
// Scope-limited to esbuild invocations only.

const cp = require('node:child_process')

const isEsbuildBinary = (file, args) => {
  try {
    if (typeof file !== 'string') return false
    // Match esbuild binary path from @esbuild/* packages and ensure it's the service mode
    const normalized = file.replace(/\\/g, '/')
    return normalized.includes('/node_modules/@esbuild/') && Array.isArray(args) && args.some(a => typeof a === 'string' && a.startsWith('--service='))
  } catch {
    return false
  }
}

const forwardStderr = (child) => {
  try {
    if (child && child.stderr && typeof child.stderr.on === 'function') {
      child.stderr.on('data', (chunk) => {
        try { process.stderr.write(chunk) } catch {}
      })
    }
  } catch {}
}

const origSpawn = cp.spawn
cp.spawn = function(...spawnArgs) {
  // Normalize Node's flexible signature: spawn(command[, args][, options])
  let file = spawnArgs[0]
  let args = []
  let options = undefined

  if (spawnArgs.length === 1) {
    // spawn(command)
    // nothing to do
  } else if (spawnArgs.length === 2) {
    // spawn(command, args|options)
    if (Array.isArray(spawnArgs[1])) args = spawnArgs[1]
    else options = spawnArgs[1]
  } else {
    // spawn(command, args, options)
    args = Array.isArray(spawnArgs[1]) ? spawnArgs[1] : []
    options = spawnArgs[2]
  }

  let tweaked = false
  if (isEsbuildBinary(file, args) && options && Array.isArray(options.stdio)) {
    const stdio = options.stdio.slice()
    if (stdio[2] === 'inherit') {
      stdio[2] = 'pipe'
      options = { ...options, stdio }
      tweaked = true
    }
  }

  // Reconstruct arguments to preserve original calling convention
  const finalArgs = [file]
  if (args && args.length) finalArgs.push(args)
  if (options !== undefined) finalArgs.push(options)

  // Enhanced logging before spawn
  try {
    console.log('[spawn-call]', { file, args: args?.slice(0, 5), options: { ...options, stdio: options?.stdio } })
  } catch {}

  let child
  try {
    child = origSpawn.apply(this, finalArgs)
  } catch (err) {
    console.error('[spawn-sync-error]', err && err.code, err && err.message, { file, args, options }, new Error('spawn sync error stack').stack)
    throw err
  }
  if (tweaked) forwardStderr(child)
  // Attach error diagnostics
  if (child && typeof child.on === 'function') {
    child.on('error', (err) => {
      try {
        const info = { file, args, options }
        const stack = new Error('spawn error stack').stack
        console.error('[spawn-error]', err && err.code, err && err.message, info, '\n', stack)
      } catch {}
    })
  }
  return child
}

const origFork = cp.fork
cp.fork = function(modulePath, args, options) {
  // Log fork calls
  try {
    console.log('[fork-call]', { modulePath, args: args?.slice(0, 5), options: { ...options, stdio: options?.stdio } })
  } catch {}
  const child = origFork.call(this, modulePath, args, options)
  if (child && typeof child.on === 'function') {
    child.on('error', (err) => {
      try {
        const info = { modulePath, args, options }
        const stack = new Error('fork error stack').stack
        console.error('[fork-error]', err && err.code, err && err.message, info, '\n', stack)
      } catch {}
    })
  }
  return child
}
