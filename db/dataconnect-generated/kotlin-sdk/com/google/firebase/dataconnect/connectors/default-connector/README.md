# Package com.google.firebase.dataconnect.connectors.default-connector

This Kotlin package provides a type-safe library
for the Firebase Data Connect connector named `turbois`,
with service ID `pib`
hosted in `us-central1`.

To use this package, it is typical to add the following imports
to each Kotlin source file that uses the connector:

```kotlin
import com.google.firebase.*
import com.google.firebase.dataconnect.*
import com.google.firebase.dataconnect.connectors.default-connector.*
```

All code samples below assume that these imports are present
in the containing file.

This code was generated by the Firebase Data Connect code generator.
Any changes made to the code, including this file, will be overwritten
the next time that code generation is performed.
See <https://firebase.google.com/docs/data-connect/android-sdk>
for more details.
Report issues to the Firebase Android SDK GitHub repository:
<https://github.com/firebase/firebase-android-sdk>.

## Data Connect SDK Setup

Before using the Data Connect SDK, you must first set up your application
to use the Firebase Android SDK in general as documented at
<https://firebase.google.com/docs/android/setup>.
This includes steps such as creating a Firebase project,
adding Firebase to your Android Studio project,
and initializing Firebase in your Kotlin code.

Then, follow the steps at
<https://firebase.google.com/docs/data-connect/android-sdk>
to set up your application specifically for using the Data Connect SDK.
This includes steps such as
adding the `com.google.firebase:firebase-dataconnect` Gradle dependency,
adding the Kotlin Serialization Gradle plugin and dependency,
and adding the Kotlin Coroutines dependency.

## TurboisConnector - The Main Class

The [TurboisConnector] class is the main entry point
for accessing the `turbois` Data Connect connector.
First, it provides methods, extension functions, and properties for retrieving
instances of itself.
Second, it provides one property for each query and each mutation
defined in the GraphQL source files.
Finally, it provides a [TurboisConnector.dataConnect] property
that provides access to the underlying
[com.google.firebase.dataconnect.FirebaseDataConnect] instance.

### TurboisConnector - Retrieving Instances

The default instance of [TurboisConnector] can be retrieved,
creating it if it does not already exist, by accessing the [instance] extension
property of the companion object of [TurboisConnector].
For example,

```kotlin
val connector: TurboisConnector = TurboisConnector.instance
```

If the default [TurboisConnector] instance is not sufficient,
an instance with different settings can be created (or retrieved) using the
[getInstance] extension function of the companion object of
[TurboisConnector].
For example,

```kotlin
val connector: TurboisConnector = TurboisConnector.getInstance(
  Firebase.app("custom-app-name")
)
```

### TurboisConnector - Query and Mutation Properties

The `turbois` Data Connect connector defines
59 queries and
74 mutations,
a total of 133 operations.
Each of these operations is exposed
as a property of [TurboisConnector].

An example of the property for a query
is the query named "BusinessProfilesBySimilarity",
which can be accessed via the [TurboisConnector.businessProfilesBySimilarity] property.

An example of the property for a mutation
is the mutation named "AcceptInvitation",
which can be accessed via the [TurboisConnector.acceptInvitation] property.

### TurboisConnector - The `dataConnect` Property

The [TurboisConnector.dataConnect] property
provides access to the underlying
[com.google.firebase.dataconnect.FirebaseDataConnect] instance.
Although basic applications will likely not need to access this property,
it is provided for advanced use cases,
such as connecting to the Data Connect emulator
(instead of the Data Connect production servers)
and "closing" the
[com.google.firebase.dataconnect.FirebaseDataConnect] instance

For example, to connect to the Data Connect emulator,
use the following code:

```kotlin
val connector = TurboisConnector.instance
connector.dataConnect.useEmulator()
```

If your application only needs access to the Data Connect service for a short
time, you can close the underlying
[com.google.firebase.dataconnect.FirebaseDataConnect] instance
to release its resources. A new instance can be retrieved later by
accessing the [instance] extension property of the companion object of
[TurboisConnector].

```kotlin
// Before close(), the `instance` property returns the same object each time.
val connector1 = TurboisConnector.instance
val connector2 = TurboisConnector.instance
check(connector1 === connector2)

connector1.dataConnect.close()

// After close(), the `instance` property returns a new instance.
val connector3 = TurboisConnector.instance
check(connector1 !== connector3)
```

### Data Connect Debug Logging

To enable debug logging for the Data Connect SDK,
set the [com.google.firebase.dataconnect.logLevel]
extension property of the companion object of
[com.google.firebase.dataconnect.FirebaseDataConnect] to
[com.google.firebase.dataconnect.FirebaseDataConnect.LogLevel.DEBUG]:

```Kotlin
FirebaseDataConnect.logLevel.value = LogLevel.DEBUG
```

With debug logging enabled,
the SDK will log verbose information to Android's "logcat" logger.
This extra information can be very helpful when troubleshooting
issues with the SDK and/or reporting issues to Google.

## Executing Queries

Executing a query is as simple as calling the `execute()` method
of the corresponding property of [TurboisConnector].
Most queries require "variables",
arguments to the query that affect its result.
Query variables are either "optional" or "required",
based on the "non-nullable" marker indicated in the GraphQL source file.
_Required_ variables are specified as arguments to the `execute()` method.
_Optional_ variables are specified in a Kotlin DSL block as the
last argument of the `execute()` method.

### Executing Queries with No Variables

If a query has no variables then it can be easily executed
by calling the `execute()` method with no arguments.

For example, the "GetAllUsers" query has no variables
and can be executed via the
[TurboisConnector.getAllUsers]
property as follows:

```kotlin
val connector = TurboisConnector.instance
val queryResult = connector.getAllUsers.execute()
println("GetAllUsers query returned: ${queryResult.data}")
```

### Executing Queries with Required Variables

If a query has _required_ variables then they must be specified as
arguments to the `execute()` method.

For example, the "GetEmbeddingByEntity" query has 3 required variables ("workspaceId", "entityType", and "entityId")
and can be executed via the [TurboisConnector.getEmbeddingByEntity]
property as follows:

```kotlin
val connector = TurboisConnector.instance
val queryResult = connector.getEmbeddingByEntity.execute(workspaceId=java.util.UUID.randomUUID(), entityType="corge", entityId="garply")
println("GetEmbeddingByEntity query returned: ${queryResult.data}")
```

### Executing Queries with Optional Variables

If a query has _optional_ variables then, by definition,
they are _not_ required to be specified to the `execute()` method;
however, if they _are_ specified,
then they are specified in a Kotlin DSL block as the last argument
of the `execute()` method.

For example, the "SearchAuditLogs" query has 9 optional variables ("workspaceId", "userId", "action", "resource", "severity", "status", "startDate", "endDate", and "limit")
and can be executed via the [TurboisConnector.searchAuditLogs]
property as follows:

```kotlin
val connector = TurboisConnector.instance
val queryResult = connector.searchAuditLogs.execute {
  workspaceId = java.util.UUID.randomUUID()
  userId = "qux"
  action = "garply"
  resource = "baz"
  severity = "thud"
  status = "garply"
  startDate = Timestamp(899322660, 958078189)
  endDate = Timestamp(1104362934, 69658357)
  limit = 2702
}
println("SearchAuditLogs query returned: ${queryResult.data}")
```

## Executing Mutations

Executing a mutation is as simple as calling the `execute()` method
of the corresponding property of [TurboisConnector].
Most mutations require "variables",
arguments to the mutation that affect its result.
Mutation variables are either "optional" or "required",
based on the "non-nullable" marker indicated in the GraphQL source file.
_Required_ variables are specified as arguments to the `execute()` method.
_Optional_ variables are specified in a Kotlin DSL block as the
last argument of the `execute()` method.

### Executing Mutations with Required Variables

If a mutation has _required_ variables then they must be specified as
arguments to the `execute()` method.

For example, the "CreateEmbedding" mutation has 6 required variables ("workspaceId", "entityType", "entityId", "contentHash", "content", and "model")
and can be executed via the [TurboisConnector.createEmbedding]
property as follows:

```kotlin
val connector = TurboisConnector.instance
val mutationResult = connector.createEmbedding.execute(workspaceId=java.util.UUID.randomUUID(), entityType="corge", entityId="garply", contentHash="quux", content="grault", model="foo")
println("CreateEmbedding mutation returned: ${mutationResult.data}")
```

### Executing Mutations with Optional Variables

If a mutation has _optional_ variables then, by definition,
they are _not_ required to be specified to the `execute()` method;
however, if they _are_ specified,
then they are specified in a Kotlin DSL block as the last argument
of the `execute()` method.

For example, the "CreateAuditLog" mutation has 10 optional variables ("workspaceId", "userId", "resource", "resourceId", "ipAddress", "userAgent", "severity", "status", "metadata", and "sessionId")
and can be executed via the [TurboisConnector.createAuditLog]
property as follows:

```kotlin
val connector = TurboisConnector.instance
val mutationResult = connector.createAuditLog.execute(action="garply") {
  workspaceId = java.util.UUID.randomUUID()
  userId = "qux"
  resource = "baz"
  resourceId = "baz"
  ipAddress = "quux"
  userAgent = "qux"
  severity = "thud"
  status = "garply"
  metadata = "thud"
  sessionId = "quux"
}
println("CreateAuditLog mutation returned: ${mutationResult.data}")
```

## Epilogue

// The lines below are used by the code generator to ensure that this file is deleted if it is no
// longer needed. Any files in this directory that contain the lines below will be deleted by the
// code generator if the file is no longer needed. If, for some reason, you do _not_ want the code
// generator to delete this file, then remove the line below (and this comment too, if you want).

// FIREBASE_DATA_CONNECT_GENERATED_FILE MARKER 42da5e14-69b3-401b-a9f1-e407bee89a78
// FIREBASE_DATA_CONNECT_GENERATED_FILE CONNECTOR turbois
