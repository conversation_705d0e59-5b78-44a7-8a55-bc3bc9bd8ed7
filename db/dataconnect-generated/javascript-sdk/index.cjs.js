const { queryRef, executeQuery, mutationRef, executeMutation, validateArgs } = require('firebase/data-connect')

const connectorConfig = {
  connector: 'turbois',
  service: 'pib',
  location: 'us-central1',
}
exports.connectorConfig = connectorConfig

function createConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateConversation', inputVars)
}
createConversationRef.operationName = 'CreateConversation'
exports.createConversationRef = createConversationRef

exports.createConversation = function createConversation(dcOrVars, vars) {
  return executeMutation(createConversationRef(dcOrVars, vars))
}

function createConversationWithSummaryRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateConversationWithSummary', inputVars)
}
createConversationWithSummaryRef.operationName = 'CreateConversationWithSummary'
exports.createConversationWithSummaryRef = createConversationWithSummaryRef

exports.createConversationWithSummary = function createConversationWithSummary(dcOrVars, vars) {
  return executeMutation(createConversationWithSummaryRef(dcOrVars, vars))
}

function addConversationParticipantRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AddConversationParticipant', inputVars)
}
addConversationParticipantRef.operationName = 'AddConversationParticipant'
exports.addConversationParticipantRef = addConversationParticipantRef

exports.addConversationParticipant = function addConversationParticipant(dcOrVars, vars) {
  return executeMutation(addConversationParticipantRef(dcOrVars, vars))
}

function createMessageRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateMessage', inputVars)
}
createMessageRef.operationName = 'CreateMessage'
exports.createMessageRef = createMessageRef

exports.createMessage = function createMessage(dcOrVars, vars) {
  return executeMutation(createMessageRef(dcOrVars, vars))
}

function createMessageWithEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateMessageWithEmbedding', inputVars)
}
createMessageWithEmbeddingRef.operationName = 'CreateMessageWithEmbedding'
exports.createMessageWithEmbeddingRef = createMessageWithEmbeddingRef

exports.createMessageWithEmbedding = function createMessageWithEmbedding(dcOrVars, vars) {
  return executeMutation(createMessageWithEmbeddingRef(dcOrVars, vars))
}

function updateConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateConversation', inputVars)
}
updateConversationRef.operationName = 'UpdateConversation'
exports.updateConversationRef = updateConversationRef

exports.updateConversation = function updateConversation(dcOrVars, vars) {
  return executeMutation(updateConversationRef(dcOrVars, vars))
}

function updateConversationWithSummaryRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateConversationWithSummary', inputVars)
}
updateConversationWithSummaryRef.operationName = 'UpdateConversationWithSummary'
exports.updateConversationWithSummaryRef = updateConversationWithSummaryRef

exports.updateConversationWithSummary = function updateConversationWithSummary(dcOrVars, vars) {
  return executeMutation(updateConversationWithSummaryRef(dcOrVars, vars))
}

function markMessagesAsReadRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'MarkMessagesAsRead', inputVars)
}
markMessagesAsReadRef.operationName = 'MarkMessagesAsRead'
exports.markMessagesAsReadRef = markMessagesAsReadRef

exports.markMessagesAsRead = function markMessagesAsRead(dcOrVars, vars) {
  return executeMutation(markMessagesAsReadRef(dcOrVars, vars))
}

function addReactionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AddReaction', inputVars)
}
addReactionRef.operationName = 'AddReaction'
exports.addReactionRef = addReactionRef

exports.addReaction = function addReaction(dcOrVars, vars) {
  return executeMutation(addReactionRef(dcOrVars, vars))
}

function removeReactionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RemoveReaction', inputVars)
}
removeReactionRef.operationName = 'RemoveReaction'
exports.removeReactionRef = removeReactionRef

exports.removeReaction = function removeReaction(dcOrVars, vars) {
  return executeMutation(removeReactionRef(dcOrVars, vars))
}

function updateConversationParticipantRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateConversationParticipant', inputVars)
}
updateConversationParticipantRef.operationName = 'UpdateConversationParticipant'
exports.updateConversationParticipantRef = updateConversationParticipantRef

exports.updateConversationParticipant = function updateConversationParticipant(dcOrVars, vars) {
  return executeMutation(updateConversationParticipantRef(dcOrVars, vars))
}

function deleteMessageRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteMessage', inputVars)
}
deleteMessageRef.operationName = 'DeleteMessage'
exports.deleteMessageRef = deleteMessageRef

exports.deleteMessage = function deleteMessage(dcOrVars, vars) {
  return executeMutation(deleteMessageRef(dcOrVars, vars))
}

function editMessageRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'EditMessage', inputVars)
}
editMessageRef.operationName = 'EditMessage'
exports.editMessageRef = editMessageRef

exports.editMessage = function editMessage(dcOrVars, vars) {
  return executeMutation(editMessageRef(dcOrVars, vars))
}

function editMessageWithEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'EditMessageWithEmbedding', inputVars)
}
editMessageWithEmbeddingRef.operationName = 'EditMessageWithEmbedding'
exports.editMessageWithEmbeddingRef = editMessageWithEmbeddingRef

exports.editMessageWithEmbedding = function editMessageWithEmbedding(dcOrVars, vars) {
  return executeMutation(editMessageWithEmbeddingRef(dcOrVars, vars))
}

function deleteConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteConversation', inputVars)
}
deleteConversationRef.operationName = 'DeleteConversation'
exports.deleteConversationRef = deleteConversationRef

exports.deleteConversation = function deleteConversation(dcOrVars, vars) {
  return executeMutation(deleteConversationRef(dcOrVars, vars))
}

function leaveConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LeaveConversation', inputVars)
}
leaveConversationRef.operationName = 'LeaveConversation'
exports.leaveConversationRef = leaveConversationRef

exports.leaveConversation = function leaveConversation(dcOrVars, vars) {
  return executeMutation(leaveConversationRef(dcOrVars, vars))
}

function removeConversationParticipantRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RemoveConversationParticipant', inputVars)
}
removeConversationParticipantRef.operationName = 'RemoveConversationParticipant'
exports.removeConversationParticipantRef = removeConversationParticipantRef

exports.removeConversationParticipant = function removeConversationParticipant(dcOrVars, vars) {
  return executeMutation(removeConversationParticipantRef(dcOrVars, vars))
}

function updateConversationAfterMessageRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateConversationAfterMessage', inputVars)
}
updateConversationAfterMessageRef.operationName = 'UpdateConversationAfterMessage'
exports.updateConversationAfterMessageRef = updateConversationAfterMessageRef

exports.updateConversationAfterMessage = function updateConversationAfterMessage(dcOrVars, vars) {
  return executeMutation(updateConversationAfterMessageRef(dcOrVars, vars))
}

function incrementUnreadCountsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'IncrementUnreadCounts', inputVars)
}
incrementUnreadCountsRef.operationName = 'IncrementUnreadCounts'
exports.incrementUnreadCountsRef = incrementUnreadCountsRef

exports.incrementUnreadCounts = function incrementUnreadCounts(dcOrVars, vars) {
  return executeMutation(incrementUnreadCountsRef(dcOrVars, vars))
}

function createDirectConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateDirectConversation', inputVars)
}
createDirectConversationRef.operationName = 'CreateDirectConversation'
exports.createDirectConversationRef = createDirectConversationRef

exports.createDirectConversation = function createDirectConversation(dcOrVars, vars) {
  return executeMutation(createDirectConversationRef(dcOrVars, vars))
}

function addMultipleParticipantsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AddMultipleParticipants', inputVars)
}
addMultipleParticipantsRef.operationName = 'AddMultipleParticipants'
exports.addMultipleParticipantsRef = addMultipleParticipantsRef

exports.addMultipleParticipants = function addMultipleParticipants(dcOrVars, vars) {
  return executeMutation(addMultipleParticipantsRef(dcOrVars, vars))
}

function getConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversation', inputVars)
}
getConversationRef.operationName = 'GetConversation'
exports.getConversationRef = getConversationRef

exports.getConversation = function getConversation(dcOrVars, vars) {
  return executeQuery(getConversationRef(dcOrVars, vars))
}

function getConversationWithParticipantsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversationWithParticipants', inputVars)
}
getConversationWithParticipantsRef.operationName = 'GetConversationWithParticipants'
exports.getConversationWithParticipantsRef = getConversationWithParticipantsRef

exports.getConversationWithParticipants = function getConversationWithParticipants(dcOrVars, vars) {
  return executeQuery(getConversationWithParticipantsRef(dcOrVars, vars))
}

function getConversationMessagesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversationMessages', inputVars)
}
getConversationMessagesRef.operationName = 'GetConversationMessages'
exports.getConversationMessagesRef = getConversationMessagesRef

exports.getConversationMessages = function getConversationMessages(dcOrVars, vars) {
  return executeQuery(getConversationMessagesRef(dcOrVars, vars))
}

function getConversationMessagesWithReactionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversationMessagesWithReactions', inputVars)
}
getConversationMessagesWithReactionsRef.operationName = 'GetConversationMessagesWithReactions'
exports.getConversationMessagesWithReactionsRef = getConversationMessagesWithReactionsRef

exports.getConversationMessagesWithReactions = function getConversationMessagesWithReactions(dcOrVars, vars) {
  return executeQuery(getConversationMessagesWithReactionsRef(dcOrVars, vars))
}

function getUserConversationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserConversations', inputVars)
}
getUserConversationsRef.operationName = 'GetUserConversations'
exports.getUserConversationsRef = getUserConversationsRef

exports.getUserConversations = function getUserConversations(dcOrVars, vars) {
  return executeQuery(getUserConversationsRef(dcOrVars, vars))
}

function getDirectConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetDirectConversation', inputVars)
}
getDirectConversationRef.operationName = 'GetDirectConversation'
exports.getDirectConversationRef = getDirectConversationRef

exports.getDirectConversation = function getDirectConversation(dcOrVars, vars) {
  return executeQuery(getDirectConversationRef(dcOrVars, vars))
}

function searchConversationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchConversations', inputVars)
}
searchConversationsRef.operationName = 'SearchConversations'
exports.searchConversationsRef = searchConversationsRef

exports.searchConversations = function searchConversations(dcOrVars, vars) {
  return executeQuery(searchConversationsRef(dcOrVars, vars))
}

function searchMessagesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchMessages', inputVars)
}
searchMessagesRef.operationName = 'SearchMessages'
exports.searchMessagesRef = searchMessagesRef

exports.searchMessages = function searchMessages(dcOrVars, vars) {
  return executeQuery(searchMessagesRef(dcOrVars, vars))
}

function searchWorkspaceMessagesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchWorkspaceMessages', inputVars)
}
searchWorkspaceMessagesRef.operationName = 'SearchWorkspaceMessages'
exports.searchWorkspaceMessagesRef = searchWorkspaceMessagesRef

exports.searchWorkspaceMessages = function searchWorkspaceMessages(dcOrVars, vars) {
  return executeQuery(searchWorkspaceMessagesRef(dcOrVars, vars))
}

function getUnreadCountsRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUnreadCounts')
}
getUnreadCountsRef.operationName = 'GetUnreadCounts'
exports.getUnreadCountsRef = getUnreadCountsRef

exports.getUnreadCounts = function getUnreadCounts(dc) {
  return executeQuery(getUnreadCountsRef(dc))
}

function getTotalUnreadCountRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetTotalUnreadCount')
}
getTotalUnreadCountRef.operationName = 'GetTotalUnreadCount'
exports.getTotalUnreadCountRef = getTotalUnreadCountRef

exports.getTotalUnreadCount = function getTotalUnreadCount(dc) {
  return executeQuery(getTotalUnreadCountRef(dc))
}

function getConversationParticipantRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversationParticipant', inputVars)
}
getConversationParticipantRef.operationName = 'GetConversationParticipant'
exports.getConversationParticipantRef = getConversationParticipantRef

exports.getConversationParticipant = function getConversationParticipant(dcOrVars, vars) {
  return executeQuery(getConversationParticipantRef(dcOrVars, vars))
}

function getMessageReactionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetMessageReactions', inputVars)
}
getMessageReactionsRef.operationName = 'GetMessageReactions'
exports.getMessageReactionsRef = getMessageReactionsRef

exports.getMessageReactions = function getMessageReactions(dcOrVars, vars) {
  return executeQuery(getMessageReactionsRef(dcOrVars, vars))
}

function getUserMessageReactionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserMessageReaction', inputVars)
}
getUserMessageReactionRef.operationName = 'GetUserMessageReaction'
exports.getUserMessageReactionRef = getUserMessageReactionRef

exports.getUserMessageReaction = function getUserMessageReaction(dcOrVars, vars) {
  return executeQuery(getUserMessageReactionRef(dcOrVars, vars))
}

function findDirectConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'FindDirectConversation', inputVars)
}
findDirectConversationRef.operationName = 'FindDirectConversation'
exports.findDirectConversationRef = findDirectConversationRef

exports.findDirectConversation = function findDirectConversation(dcOrVars, vars) {
  return executeQuery(findDirectConversationRef(dcOrVars, vars))
}

function getAiChatConversationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAIChatConversations', inputVars)
}
getAiChatConversationsRef.operationName = 'GetAIChatConversations'
exports.getAiChatConversationsRef = getAiChatConversationsRef

exports.getAiChatConversations = function getAiChatConversations(dcOrVars, vars) {
  return executeQuery(getAiChatConversationsRef(dcOrVars, vars))
}

function createUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateUser', inputVars)
}
createUserRef.operationName = 'CreateUser'
exports.createUserRef = createUserRef

exports.createUser = function createUser(dcOrVars, vars) {
  return executeMutation(createUserRef(dcOrVars, vars))
}

function createUserForSignupRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateUserForSignup', inputVars)
}
createUserForSignupRef.operationName = 'CreateUserForSignup'
exports.createUserForSignupRef = createUserForSignupRef

exports.createUserForSignup = function createUserForSignup(dcOrVars, vars) {
  return executeMutation(createUserForSignupRef(dcOrVars, vars))
}

function updateUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateUser', inputVars)
}
updateUserRef.operationName = 'UpdateUser'
exports.updateUserRef = updateUserRef

exports.updateUser = function updateUser(dcOrVars, vars) {
  return executeMutation(updateUserRef(dcOrVars, vars))
}

function createProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateProfile', inputVars)
}
createProfileRef.operationName = 'CreateProfile'
exports.createProfileRef = createProfileRef

exports.createProfile = function createProfile(dcOrVars, vars) {
  return executeMutation(createProfileRef(dcOrVars, vars))
}

function createProfileForSignupRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateProfileForSignup', inputVars)
}
createProfileForSignupRef.operationName = 'CreateProfileForSignup'
exports.createProfileForSignupRef = createProfileForSignupRef

exports.createProfileForSignup = function createProfileForSignup(dcOrVars, vars) {
  return executeMutation(createProfileForSignupRef(dcOrVars, vars))
}

function createProfileWithBioRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateProfileWithBio', inputVars)
}
createProfileWithBioRef.operationName = 'CreateProfileWithBio'
exports.createProfileWithBioRef = createProfileWithBioRef

exports.createProfileWithBio = function createProfileWithBio(dcOrVars, vars) {
  return executeMutation(createProfileWithBioRef(dcOrVars, vars))
}

function updateProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateProfile', inputVars)
}
updateProfileRef.operationName = 'UpdateProfile'
exports.updateProfileRef = updateProfileRef

exports.updateProfile = function updateProfile(dcOrVars, vars) {
  return executeMutation(updateProfileRef(dcOrVars, vars))
}

function updateProfileWithBioRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateProfileWithBio', inputVars)
}
updateProfileWithBioRef.operationName = 'UpdateProfileWithBio'
exports.updateProfileWithBioRef = updateProfileWithBioRef

exports.updateProfileWithBio = function updateProfileWithBio(dcOrVars, vars) {
  return executeMutation(updateProfileWithBioRef(dcOrVars, vars))
}

function deleteProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteProfile', inputVars)
}
deleteProfileRef.operationName = 'DeleteProfile'
exports.deleteProfileRef = deleteProfileRef

exports.deleteProfile = function deleteProfile(dcOrVars, vars) {
  return executeMutation(deleteProfileRef(dcOrVars, vars))
}

function createWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateWorkspace', inputVars)
}
createWorkspaceRef.operationName = 'CreateWorkspace'
exports.createWorkspaceRef = createWorkspaceRef

exports.createWorkspace = function createWorkspace(dcOrVars, vars) {
  return executeMutation(createWorkspaceRef(dcOrVars, vars))
}

function createWorkspaceForSignupRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateWorkspaceForSignup', inputVars)
}
createWorkspaceForSignupRef.operationName = 'CreateWorkspaceForSignup'
exports.createWorkspaceForSignupRef = createWorkspaceForSignupRef

exports.createWorkspaceForSignup = function createWorkspaceForSignup(dcOrVars, vars) {
  return executeMutation(createWorkspaceForSignupRef(dcOrVars, vars))
}

function updateWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateWorkspace', inputVars)
}
updateWorkspaceRef.operationName = 'UpdateWorkspace'
exports.updateWorkspaceRef = updateWorkspaceRef

exports.updateWorkspace = function updateWorkspace(dcOrVars, vars) {
  return executeMutation(updateWorkspaceRef(dcOrVars, vars))
}

function deleteWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteWorkspace', inputVars)
}
deleteWorkspaceRef.operationName = 'DeleteWorkspace'
exports.deleteWorkspaceRef = deleteWorkspaceRef

exports.deleteWorkspace = function deleteWorkspace(dcOrVars, vars) {
  return executeMutation(deleteWorkspaceRef(dcOrVars, vars))
}

function addWorkspaceMemberRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AddWorkspaceMember', inputVars)
}
addWorkspaceMemberRef.operationName = 'AddWorkspaceMember'
exports.addWorkspaceMemberRef = addWorkspaceMemberRef

exports.addWorkspaceMember = function addWorkspaceMember(dcOrVars, vars) {
  return executeMutation(addWorkspaceMemberRef(dcOrVars, vars))
}

function updateWorkspaceMemberRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateWorkspaceMember', inputVars)
}
updateWorkspaceMemberRef.operationName = 'UpdateWorkspaceMember'
exports.updateWorkspaceMemberRef = updateWorkspaceMemberRef

exports.updateWorkspaceMember = function updateWorkspaceMember(dcOrVars, vars) {
  return executeMutation(updateWorkspaceMemberRef(dcOrVars, vars))
}

function removeWorkspaceMemberRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RemoveWorkspaceMember', inputVars)
}
removeWorkspaceMemberRef.operationName = 'RemoveWorkspaceMember'
exports.removeWorkspaceMemberRef = removeWorkspaceMemberRef

exports.removeWorkspaceMember = function removeWorkspaceMember(dcOrVars, vars) {
  return executeMutation(removeWorkspaceMemberRef(dcOrVars, vars))
}

function inviteToWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'InviteToWorkspace', inputVars)
}
inviteToWorkspaceRef.operationName = 'InviteToWorkspace'
exports.inviteToWorkspaceRef = inviteToWorkspaceRef

exports.inviteToWorkspace = function inviteToWorkspace(dcOrVars, vars) {
  return executeMutation(inviteToWorkspaceRef(dcOrVars, vars))
}

function acceptInvitationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AcceptInvitation', inputVars)
}
acceptInvitationRef.operationName = 'AcceptInvitation'
exports.acceptInvitationRef = acceptInvitationRef

exports.acceptInvitation = function acceptInvitation(dcOrVars, vars) {
  return executeMutation(acceptInvitationRef(dcOrVars, vars))
}

function declineInvitationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeclineInvitation', inputVars)
}
declineInvitationRef.operationName = 'DeclineInvitation'
exports.declineInvitationRef = declineInvitationRef

exports.declineInvitation = function declineInvitation(dcOrVars, vars) {
  return executeMutation(declineInvitationRef(dcOrVars, vars))
}

function createBusinessProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateBusinessProfile', inputVars)
}
createBusinessProfileRef.operationName = 'CreateBusinessProfile'
exports.createBusinessProfileRef = createBusinessProfileRef

exports.createBusinessProfile = function createBusinessProfile(dcOrVars, vars) {
  return executeMutation(createBusinessProfileRef(dcOrVars, vars))
}

function createBusinessProfileWithDescriptionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateBusinessProfileWithDescription', inputVars)
}
createBusinessProfileWithDescriptionRef.operationName = 'CreateBusinessProfileWithDescription'
exports.createBusinessProfileWithDescriptionRef = createBusinessProfileWithDescriptionRef

exports.createBusinessProfileWithDescription = function createBusinessProfileWithDescription(dcOrVars, vars) {
  return executeMutation(createBusinessProfileWithDescriptionRef(dcOrVars, vars))
}

function updateBusinessProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateBusinessProfile', inputVars)
}
updateBusinessProfileRef.operationName = 'UpdateBusinessProfile'
exports.updateBusinessProfileRef = updateBusinessProfileRef

exports.updateBusinessProfile = function updateBusinessProfile(dcOrVars, vars) {
  return executeMutation(updateBusinessProfileRef(dcOrVars, vars))
}

function updateBusinessProfileWithDescriptionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateBusinessProfileWithDescription', inputVars)
}
updateBusinessProfileWithDescriptionRef.operationName = 'UpdateBusinessProfileWithDescription'
exports.updateBusinessProfileWithDescriptionRef = updateBusinessProfileWithDescriptionRef

exports.updateBusinessProfileWithDescription = function updateBusinessProfileWithDescription(dcOrVars, vars) {
  return executeMutation(updateBusinessProfileWithDescriptionRef(dcOrVars, vars))
}

function createPartnerPreferencesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreatePartnerPreferences', inputVars)
}
createPartnerPreferencesRef.operationName = 'CreatePartnerPreferences'
exports.createPartnerPreferencesRef = createPartnerPreferencesRef

exports.createPartnerPreferences = function createPartnerPreferences(dcOrVars, vars) {
  return executeMutation(createPartnerPreferencesRef(dcOrVars, vars))
}

function createPartnerPreferencesWithEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreatePartnerPreferencesWithEmbedding', inputVars)
}
createPartnerPreferencesWithEmbeddingRef.operationName = 'CreatePartnerPreferencesWithEmbedding'
exports.createPartnerPreferencesWithEmbeddingRef = createPartnerPreferencesWithEmbeddingRef

exports.createPartnerPreferencesWithEmbedding = function createPartnerPreferencesWithEmbedding(dcOrVars, vars) {
  return executeMutation(createPartnerPreferencesWithEmbeddingRef(dcOrVars, vars))
}

function updatePartnerPreferencesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdatePartnerPreferences', inputVars)
}
updatePartnerPreferencesRef.operationName = 'UpdatePartnerPreferences'
exports.updatePartnerPreferencesRef = updatePartnerPreferencesRef

exports.updatePartnerPreferences = function updatePartnerPreferences(dcOrVars, vars) {
  return executeMutation(updatePartnerPreferencesRef(dcOrVars, vars))
}

function updatePartnerPreferencesWithEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdatePartnerPreferencesWithEmbedding', inputVars)
}
updatePartnerPreferencesWithEmbeddingRef.operationName = 'UpdatePartnerPreferencesWithEmbedding'
exports.updatePartnerPreferencesWithEmbeddingRef = updatePartnerPreferencesWithEmbeddingRef

exports.updatePartnerPreferencesWithEmbedding = function updatePartnerPreferencesWithEmbedding(dcOrVars, vars) {
  return executeMutation(updatePartnerPreferencesWithEmbeddingRef(dcOrVars, vars))
}

function createLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateLLMKey', inputVars)
}
createLlmKeyRef.operationName = 'CreateLLMKey'
exports.createLlmKeyRef = createLlmKeyRef

exports.createLlmKey = function createLlmKey(dcOrVars, vars) {
  return executeMutation(createLlmKeyRef(dcOrVars, vars))
}

function updateLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateLLMKey', inputVars)
}
updateLlmKeyRef.operationName = 'UpdateLLMKey'
exports.updateLlmKeyRef = updateLlmKeyRef

exports.updateLlmKey = function updateLlmKey(dcOrVars, vars) {
  return executeMutation(updateLlmKeyRef(dcOrVars, vars))
}

function deleteLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteLLMKey', inputVars)
}
deleteLlmKeyRef.operationName = 'DeleteLLMKey'
exports.deleteLlmKeyRef = deleteLlmKeyRef

exports.deleteLlmKey = function deleteLlmKey(dcOrVars, vars) {
  return executeMutation(deleteLlmKeyRef(dcOrVars, vars))
}

function createAuditLogRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateAuditLog', inputVars)
}
createAuditLogRef.operationName = 'CreateAuditLog'
exports.createAuditLogRef = createAuditLogRef

exports.createAuditLog = function createAuditLog(dcOrVars, vars) {
  return executeMutation(createAuditLogRef(dcOrVars, vars))
}

function createUserSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateUserSession', inputVars)
}
createUserSessionRef.operationName = 'CreateUserSession'
exports.createUserSessionRef = createUserSessionRef

exports.createUserSession = function createUserSession(dcOrVars, vars) {
  return executeMutation(createUserSessionRef(dcOrVars, vars))
}

function updateUserSessionActivityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateUserSessionActivity', inputVars)
}
updateUserSessionActivityRef.operationName = 'UpdateUserSessionActivity'
exports.updateUserSessionActivityRef = updateUserSessionActivityRef

exports.updateUserSessionActivity = function updateUserSessionActivity(dcOrVars, vars) {
  return executeMutation(updateUserSessionActivityRef(dcOrVars, vars))
}

function revokeUserSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RevokeUserSession', inputVars)
}
revokeUserSessionRef.operationName = 'RevokeUserSession'
exports.revokeUserSessionRef = revokeUserSessionRef

exports.revokeUserSession = function revokeUserSession(dcOrVars, vars) {
  return executeMutation(revokeUserSessionRef(dcOrVars, vars))
}

function deleteUserSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteUserSession', inputVars)
}
deleteUserSessionRef.operationName = 'DeleteUserSession'
exports.deleteUserSessionRef = deleteUserSessionRef

exports.deleteUserSession = function deleteUserSession(dcOrVars, vars) {
  return executeMutation(deleteUserSessionRef(dcOrVars, vars))
}

function createAnalyticsEventRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateAnalyticsEvent', inputVars)
}
createAnalyticsEventRef.operationName = 'CreateAnalyticsEvent'
exports.createAnalyticsEventRef = createAnalyticsEventRef

exports.createAnalyticsEvent = function createAnalyticsEvent(dcOrVars, vars) {
  return executeMutation(createAnalyticsEventRef(dcOrVars, vars))
}

function createEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateEmbedding', inputVars)
}
createEmbeddingRef.operationName = 'CreateEmbedding'
exports.createEmbeddingRef = createEmbeddingRef

exports.createEmbedding = function createEmbedding(dcOrVars, vars) {
  return executeMutation(createEmbeddingRef(dcOrVars, vars))
}

function updateEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateEmbedding', inputVars)
}
updateEmbeddingRef.operationName = 'UpdateEmbedding'
exports.updateEmbeddingRef = updateEmbeddingRef

exports.updateEmbedding = function updateEmbedding(dcOrVars, vars) {
  return executeMutation(updateEmbeddingRef(dcOrVars, vars))
}

function softDeleteEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'SoftDeleteEmbedding', inputVars)
}
softDeleteEmbeddingRef.operationName = 'SoftDeleteEmbedding'
exports.softDeleteEmbeddingRef = softDeleteEmbeddingRef

exports.softDeleteEmbedding = function softDeleteEmbedding(dcOrVars, vars) {
  return executeMutation(softDeleteEmbeddingRef(dcOrVars, vars))
}

function deleteEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteEmbedding', inputVars)
}
deleteEmbeddingRef.operationName = 'DeleteEmbedding'
exports.deleteEmbeddingRef = deleteEmbeddingRef

exports.deleteEmbedding = function deleteEmbedding(dcOrVars, vars) {
  return executeMutation(deleteEmbeddingRef(dcOrVars, vars))
}

function createEmbeddingBatchRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateEmbeddingBatch', inputVars)
}
createEmbeddingBatchRef.operationName = 'CreateEmbeddingBatch'
exports.createEmbeddingBatchRef = createEmbeddingBatchRef

exports.createEmbeddingBatch = function createEmbeddingBatch(dcOrVars, vars) {
  return executeMutation(createEmbeddingBatchRef(dcOrVars, vars))
}

function getAllUsersRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAllUsers')
}
getAllUsersRef.operationName = 'GetAllUsers'
exports.getAllUsersRef = getAllUsersRef

exports.getAllUsers = function getAllUsers(dc) {
  return executeQuery(getAllUsersRef(dc))
}

function getCurrentUserRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetCurrentUser')
}
getCurrentUserRef.operationName = 'GetCurrentUser'
exports.getCurrentUserRef = getCurrentUserRef

exports.getCurrentUser = function getCurrentUser(dc) {
  return executeQuery(getCurrentUserRef(dc))
}

function searchProfilesByBioRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchProfilesByBio', inputVars)
}
searchProfilesByBioRef.operationName = 'SearchProfilesByBio'
exports.searchProfilesByBioRef = searchProfilesByBioRef

exports.searchProfilesByBio = function searchProfilesByBio(dcOrVars, vars) {
  return executeQuery(searchProfilesByBioRef(dcOrVars, vars))
}

function searchBusinessProfilesByDescriptionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchBusinessProfilesByDescription', inputVars)
}
searchBusinessProfilesByDescriptionRef.operationName = 'SearchBusinessProfilesByDescription'
exports.searchBusinessProfilesByDescriptionRef = searchBusinessProfilesByDescriptionRef

exports.searchBusinessProfilesByDescription = function searchBusinessProfilesByDescription(dcOrVars, vars) {
  return executeQuery(searchBusinessProfilesByDescriptionRef(dcOrVars, vars))
}

function matchProfileToBusinessesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'MatchProfileToBusinesses', inputVars)
}
matchProfileToBusinessesRef.operationName = 'MatchProfileToBusinesses'
exports.matchProfileToBusinessesRef = matchProfileToBusinessesRef

exports.matchProfileToBusinesses = function matchProfileToBusinesses(dcOrVars, vars) {
  return executeQuery(matchProfileToBusinessesRef(dcOrVars, vars))
}

function businessProfilesBySimilarityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'BusinessProfilesBySimilarity', inputVars)
}
businessProfilesBySimilarityRef.operationName = 'BusinessProfilesBySimilarity'
exports.businessProfilesBySimilarityRef = businessProfilesBySimilarityRef

exports.businessProfilesBySimilarity = function businessProfilesBySimilarity(dcOrVars, vars) {
  return executeQuery(businessProfilesBySimilarityRef(dcOrVars, vars))
}

function matchBusinessToProfilesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'MatchBusinessToProfiles', inputVars)
}
matchBusinessToProfilesRef.operationName = 'MatchBusinessToProfiles'
exports.matchBusinessToProfilesRef = matchBusinessToProfilesRef

exports.matchBusinessToProfiles = function matchBusinessToProfiles(dcOrVars, vars) {
  return executeQuery(matchBusinessToProfilesRef(dcOrVars, vars))
}

function profilesBySimilarityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'ProfilesBySimilarity', inputVars)
}
profilesBySimilarityRef.operationName = 'ProfilesBySimilarity'
exports.profilesBySimilarityRef = profilesBySimilarityRef

exports.profilesBySimilarity = function profilesBySimilarity(dcOrVars, vars) {
  return executeQuery(profilesBySimilarityRef(dcOrVars, vars))
}

function searchPartnerPreferencesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchPartnerPreferences', inputVars)
}
searchPartnerPreferencesRef.operationName = 'SearchPartnerPreferences'
exports.searchPartnerPreferencesRef = searchPartnerPreferencesRef

exports.searchPartnerPreferences = function searchPartnerPreferences(dcOrVars, vars) {
  return executeQuery(searchPartnerPreferencesRef(dcOrVars, vars))
}

function getUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUser', inputVars)
}
getUserRef.operationName = 'GetUser'
exports.getUserRef = getUserRef

exports.getUser = function getUser(dcOrVars, vars) {
  return executeQuery(getUserRef(dcOrVars, vars))
}

function getUserProfilesRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserProfiles')
}
getUserProfilesRef.operationName = 'GetUserProfiles'
exports.getUserProfilesRef = getUserProfilesRef

exports.getUserProfiles = function getUserProfiles(dc) {
  return executeQuery(getUserProfilesRef(dc))
}

function getUserWorkspacesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserWorkspaces', inputVars)
}
getUserWorkspacesRef.operationName = 'GetUserWorkspaces'
exports.getUserWorkspacesRef = getUserWorkspacesRef

exports.getUserWorkspaces = function getUserWorkspaces(dcOrVars, vars) {
  return executeQuery(getUserWorkspacesRef(dcOrVars, vars))
}

function getWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspace', inputVars)
}
getWorkspaceRef.operationName = 'GetWorkspace'
exports.getWorkspaceRef = getWorkspaceRef

exports.getWorkspace = function getWorkspace(dcOrVars, vars) {
  return executeQuery(getWorkspaceRef(dcOrVars, vars))
}

function getWorkspaceMembersRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspaceMembers', inputVars)
}
getWorkspaceMembersRef.operationName = 'GetWorkspaceMembers'
exports.getWorkspaceMembersRef = getWorkspaceMembersRef

exports.getWorkspaceMembers = function getWorkspaceMembers(dcOrVars, vars) {
  return executeQuery(getWorkspaceMembersRef(dcOrVars, vars))
}

function getBusinessProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetBusinessProfile', inputVars)
}
getBusinessProfileRef.operationName = 'GetBusinessProfile'
exports.getBusinessProfileRef = getBusinessProfileRef

exports.getBusinessProfile = function getBusinessProfile(dcOrVars, vars) {
  return executeQuery(getBusinessProfileRef(dcOrVars, vars))
}

function getPartnerPreferencesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetPartnerPreferences', inputVars)
}
getPartnerPreferencesRef.operationName = 'GetPartnerPreferences'
exports.getPartnerPreferencesRef = getPartnerPreferencesRef

exports.getPartnerPreferences = function getPartnerPreferences(dcOrVars, vars) {
  return executeQuery(getPartnerPreferencesRef(dcOrVars, vars))
}

function getWorkspaceInvitationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspaceInvitations', inputVars)
}
getWorkspaceInvitationsRef.operationName = 'GetWorkspaceInvitations'
exports.getWorkspaceInvitationsRef = getWorkspaceInvitationsRef

exports.getWorkspaceInvitations = function getWorkspaceInvitations(dcOrVars, vars) {
  return executeQuery(getWorkspaceInvitationsRef(dcOrVars, vars))
}

function getPendingInvitationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetPendingInvitations', inputVars)
}
getPendingInvitationsRef.operationName = 'GetPendingInvitations'
exports.getPendingInvitationsRef = getPendingInvitationsRef

exports.getPendingInvitations = function getPendingInvitations(dcOrVars, vars) {
  return executeQuery(getPendingInvitationsRef(dcOrVars, vars))
}

function getLlmKeysByEntityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetLLMKeysByEntity', inputVars)
}
getLlmKeysByEntityRef.operationName = 'GetLLMKeysByEntity'
exports.getLlmKeysByEntityRef = getLlmKeysByEntityRef

exports.getLlmKeysByEntity = function getLlmKeysByEntity(dcOrVars, vars) {
  return executeQuery(getLlmKeysByEntityRef(dcOrVars, vars))
}

function getLlmKeyByProviderAndEntityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetLLMKeyByProviderAndEntity', inputVars)
}
getLlmKeyByProviderAndEntityRef.operationName = 'GetLLMKeyByProviderAndEntity'
exports.getLlmKeyByProviderAndEntityRef = getLlmKeyByProviderAndEntityRef

exports.getLlmKeyByProviderAndEntity = function getLlmKeyByProviderAndEntity(dcOrVars, vars) {
  return executeQuery(getLlmKeyByProviderAndEntityRef(dcOrVars, vars))
}

function getWorkspaceLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspaceLLMKey', inputVars)
}
getWorkspaceLlmKeyRef.operationName = 'GetWorkspaceLLMKey'
exports.getWorkspaceLlmKeyRef = getWorkspaceLlmKeyRef

exports.getWorkspaceLlmKey = function getWorkspaceLlmKey(dcOrVars, vars) {
  return executeQuery(getWorkspaceLlmKeyRef(dcOrVars, vars))
}

function getUserLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserLLMKey', inputVars)
}
getUserLlmKeyRef.operationName = 'GetUserLLMKey'
exports.getUserLlmKeyRef = getUserLlmKeyRef

exports.getUserLlmKey = function getUserLlmKey(dcOrVars, vars) {
  return executeQuery(getUserLlmKeyRef(dcOrVars, vars))
}

function getProfileLlmKeysRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetProfileLLMKeys', inputVars)
}
getProfileLlmKeysRef.operationName = 'GetProfileLLMKeys'
exports.getProfileLlmKeysRef = getProfileLlmKeysRef

exports.getProfileLlmKeys = function getProfileLlmKeys(dcOrVars, vars) {
  return executeQuery(getProfileLlmKeysRef(dcOrVars, vars))
}

function getAuditLogsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAuditLogs', inputVars)
}
getAuditLogsRef.operationName = 'GetAuditLogs'
exports.getAuditLogsRef = getAuditLogsRef

exports.getAuditLogs = function getAuditLogs(dcOrVars, vars) {
  return executeQuery(getAuditLogsRef(dcOrVars, vars))
}

function getAuditLogsByUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAuditLogsByUser', inputVars)
}
getAuditLogsByUserRef.operationName = 'GetAuditLogsByUser'
exports.getAuditLogsByUserRef = getAuditLogsByUserRef

exports.getAuditLogsByUser = function getAuditLogsByUser(dcOrVars, vars) {
  return executeQuery(getAuditLogsByUserRef(dcOrVars, vars))
}

function getAuditLogsByWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAuditLogsByWorkspace', inputVars)
}
getAuditLogsByWorkspaceRef.operationName = 'GetAuditLogsByWorkspace'
exports.getAuditLogsByWorkspaceRef = getAuditLogsByWorkspaceRef

exports.getAuditLogsByWorkspace = function getAuditLogsByWorkspace(dcOrVars, vars) {
  return executeQuery(getAuditLogsByWorkspaceRef(dcOrVars, vars))
}

function getAuditLogsBySeverityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAuditLogsBySeverity', inputVars)
}
getAuditLogsBySeverityRef.operationName = 'GetAuditLogsBySeverity'
exports.getAuditLogsBySeverityRef = getAuditLogsBySeverityRef

exports.getAuditLogsBySeverity = function getAuditLogsBySeverity(dcOrVars, vars) {
  return executeQuery(getAuditLogsBySeverityRef(dcOrVars, vars))
}

function getActiveSessionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetActiveSessions', inputVars)
}
getActiveSessionsRef.operationName = 'GetActiveSessions'
exports.getActiveSessionsRef = getActiveSessionsRef

exports.getActiveSessions = function getActiveSessions(dcOrVars, vars) {
  return executeQuery(getActiveSessionsRef(dcOrVars, vars))
}

function getSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetSession', inputVars)
}
getSessionRef.operationName = 'GetSession'
exports.getSessionRef = getSessionRef

exports.getSession = function getSession(dcOrVars, vars) {
  return executeQuery(getSessionRef(dcOrVars, vars))
}

function getUserSessionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserSessions', inputVars)
}
getUserSessionsRef.operationName = 'GetUserSessions'
exports.getUserSessionsRef = getUserSessionsRef

exports.getUserSessions = function getUserSessions(dcOrVars, vars) {
  return executeQuery(getUserSessionsRef(dcOrVars, vars))
}

function getEmbeddingsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetEmbeddings', inputVars)
}
getEmbeddingsRef.operationName = 'GetEmbeddings'
exports.getEmbeddingsRef = getEmbeddingsRef

exports.getEmbeddings = function getEmbeddings(dcOrVars, vars) {
  return executeQuery(getEmbeddingsRef(dcOrVars, vars))
}

function getEmbeddingByEntityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetEmbeddingByEntity', inputVars)
}
getEmbeddingByEntityRef.operationName = 'GetEmbeddingByEntity'
exports.getEmbeddingByEntityRef = getEmbeddingByEntityRef

exports.getEmbeddingByEntity = function getEmbeddingByEntity(dcOrVars, vars) {
  return executeQuery(getEmbeddingByEntityRef(dcOrVars, vars))
}

function searchEmbeddingsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchEmbeddings', inputVars)
}
searchEmbeddingsRef.operationName = 'SearchEmbeddings'
exports.searchEmbeddingsRef = searchEmbeddingsRef

exports.searchEmbeddings = function searchEmbeddings(dcOrVars, vars) {
  return executeQuery(searchEmbeddingsRef(dcOrVars, vars))
}

function createUploadRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateUpload', inputVars)
}
createUploadRef.operationName = 'CreateUpload'
exports.createUploadRef = createUploadRef

exports.createUpload = function createUpload(dcOrVars, vars) {
  return executeMutation(createUploadRef(dcOrVars, vars))
}

function linkUserPhotoUploadRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LinkUserPhotoUpload', inputVars)
}
linkUserPhotoUploadRef.operationName = 'LinkUserPhotoUpload'
exports.linkUserPhotoUploadRef = linkUserPhotoUploadRef

exports.linkUserPhotoUpload = function linkUserPhotoUpload(dcOrVars, vars) {
  return executeMutation(linkUserPhotoUploadRef(dcOrVars, vars))
}

function listUploadsByWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'ListUploadsByWorkspace', inputVars)
}
listUploadsByWorkspaceRef.operationName = 'ListUploadsByWorkspace'
exports.listUploadsByWorkspaceRef = listUploadsByWorkspaceRef

exports.listUploadsByWorkspace = function listUploadsByWorkspace(dcOrVars, vars) {
  return executeQuery(listUploadsByWorkspaceRef(dcOrVars, vars))
}

function getUploadRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUpload', inputVars)
}
getUploadRef.operationName = 'GetUpload'
exports.getUploadRef = getUploadRef

exports.getUpload = function getUpload(dcOrVars, vars) {
  return executeQuery(getUploadRef(dcOrVars, vars))
}

function logSecurityEventRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogSecurityEvent', inputVars)
}
logSecurityEventRef.operationName = 'LogSecurityEvent'
exports.logSecurityEventRef = logSecurityEventRef

exports.logSecurityEvent = function logSecurityEvent(dcOrVars, vars) {
  return executeMutation(logSecurityEventRef(dcOrVars, vars))
}

function logAuthenticationEventRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogAuthenticationEvent', inputVars)
}
logAuthenticationEventRef.operationName = 'LogAuthenticationEvent'
exports.logAuthenticationEventRef = logAuthenticationEventRef

exports.logAuthenticationEvent = function logAuthenticationEvent(dcOrVars, vars) {
  return executeMutation(logAuthenticationEventRef(dcOrVars, vars))
}

function logFailedAuthenticationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogFailedAuthentication', inputVars)
}
logFailedAuthenticationRef.operationName = 'LogFailedAuthentication'
exports.logFailedAuthenticationRef = logFailedAuthenticationRef

exports.logFailedAuthentication = function logFailedAuthentication(dcOrVars, vars) {
  return executeMutation(logFailedAuthenticationRef(dcOrVars, vars))
}

function logDataAccessRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogDataAccess', inputVars)
}
logDataAccessRef.operationName = 'LogDataAccess'
exports.logDataAccessRef = logDataAccessRef

exports.logDataAccess = function logDataAccess(dcOrVars, vars) {
  return executeMutation(logDataAccessRef(dcOrVars, vars))
}

function logPermissionChangeRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogPermissionChange', inputVars)
}
logPermissionChangeRef.operationName = 'LogPermissionChange'
exports.logPermissionChangeRef = logPermissionChangeRef

exports.logPermissionChange = function logPermissionChange(dcOrVars, vars) {
  return executeMutation(logPermissionChangeRef(dcOrVars, vars))
}

function createSecureSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateSecureSession', inputVars)
}
createSecureSessionRef.operationName = 'CreateSecureSession'
exports.createSecureSessionRef = createSecureSessionRef

exports.createSecureSession = function createSecureSession(dcOrVars, vars) {
  return executeMutation(createSecureSessionRef(dcOrVars, vars))
}

function revokeSessionWithAuditRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RevokeSessionWithAudit', inputVars)
}
revokeSessionWithAuditRef.operationName = 'RevokeSessionWithAudit'
exports.revokeSessionWithAuditRef = revokeSessionWithAuditRef

exports.revokeSessionWithAudit = function revokeSessionWithAudit(dcOrVars, vars) {
  return executeMutation(revokeSessionWithAuditRef(dcOrVars, vars))
}

function logBatchDataOperationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogBatchDataOperation', inputVars)
}
logBatchDataOperationRef.operationName = 'LogBatchDataOperation'
exports.logBatchDataOperationRef = logBatchDataOperationRef

exports.logBatchDataOperation = function logBatchDataOperation(dcOrVars, vars) {
  return executeMutation(logBatchDataOperationRef(dcOrVars, vars))
}

function logWorkspaceEventRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogWorkspaceEvent', inputVars)
}
logWorkspaceEventRef.operationName = 'LogWorkspaceEvent'
exports.logWorkspaceEventRef = logWorkspaceEventRef

exports.logWorkspaceEvent = function logWorkspaceEvent(dcOrVars, vars) {
  return executeMutation(logWorkspaceEventRef(dcOrVars, vars))
}

function emergencyRevokeAllUserSessionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'EmergencyRevokeAllUserSessions', inputVars)
}
emergencyRevokeAllUserSessionsRef.operationName = 'EmergencyRevokeAllUserSessions'
exports.emergencyRevokeAllUserSessionsRef = emergencyRevokeAllUserSessionsRef

exports.emergencyRevokeAllUserSessions = function emergencyRevokeAllUserSessions(dcOrVars, vars) {
  return executeMutation(emergencyRevokeAllUserSessionsRef(dcOrVars, vars))
}

function searchAuditLogsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchAuditLogs', inputVars)
}
searchAuditLogsRef.operationName = 'SearchAuditLogs'
exports.searchAuditLogsRef = searchAuditLogsRef

exports.searchAuditLogs = function searchAuditLogs(dcOrVars, vars) {
  return executeQuery(searchAuditLogsRef(dcOrVars, vars))
}

function getFailedLoginAttemptsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetFailedLoginAttempts', inputVars)
}
getFailedLoginAttemptsRef.operationName = 'GetFailedLoginAttempts'
exports.getFailedLoginAttemptsRef = getFailedLoginAttemptsRef

exports.getFailedLoginAttempts = function getFailedLoginAttempts(dcOrVars, vars) {
  return executeQuery(getFailedLoginAttemptsRef(dcOrVars, vars))
}

function getSuspiciousActivityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetSuspiciousActivity', inputVars)
}
getSuspiciousActivityRef.operationName = 'GetSuspiciousActivity'
exports.getSuspiciousActivityRef = getSuspiciousActivityRef

exports.getSuspiciousActivity = function getSuspiciousActivity(dcOrVars, vars) {
  return executeQuery(getSuspiciousActivityRef(dcOrVars, vars))
}

function getActiveSessionsForUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetActiveSessionsForUser', inputVars)
}
getActiveSessionsForUserRef.operationName = 'GetActiveSessionsForUser'
exports.getActiveSessionsForUserRef = getActiveSessionsForUserRef

exports.getActiveSessionsForUser = function getActiveSessionsForUser(dcOrVars, vars) {
  return executeQuery(getActiveSessionsForUserRef(dcOrVars, vars))
}

function getConcurrentSessionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConcurrentSessions', inputVars)
}
getConcurrentSessionsRef.operationName = 'GetConcurrentSessions'
exports.getConcurrentSessionsRef = getConcurrentSessionsRef

exports.getConcurrentSessions = function getConcurrentSessions(dcOrVars, vars) {
  return executeQuery(getConcurrentSessionsRef(dcOrVars, vars))
}

function getSessionsByIpRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetSessionsByIP', inputVars)
}
getSessionsByIpRef.operationName = 'GetSessionsByIP'
exports.getSessionsByIpRef = getSessionsByIpRef

exports.getSessionsByIp = function getSessionsByIp(dcOrVars, vars) {
  return executeQuery(getSessionsByIpRef(dcOrVars, vars))
}

function getWorkspaceSecurityEventsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspaceSecurityEvents', inputVars)
}
getWorkspaceSecurityEventsRef.operationName = 'GetWorkspaceSecurityEvents'
exports.getWorkspaceSecurityEventsRef = getWorkspaceSecurityEventsRef

exports.getWorkspaceSecurityEvents = function getWorkspaceSecurityEvents(dcOrVars, vars) {
  return executeQuery(getWorkspaceSecurityEventsRef(dcOrVars, vars))
}

function getDataAccessLogsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetDataAccessLogs', inputVars)
}
getDataAccessLogsRef.operationName = 'GetDataAccessLogs'
exports.getDataAccessLogsRef = getDataAccessLogsRef

exports.getDataAccessLogs = function getDataAccessLogs(dcOrVars, vars) {
  return executeQuery(getDataAccessLogsRef(dcOrVars, vars))
}
