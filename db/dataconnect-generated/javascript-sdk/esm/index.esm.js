import { executeMutation, executeQuery, mutationRef, queryRef, validateArgs } from 'firebase/data-connect'

export const connectorConfig = {
  connector: 'turbois',
  service: 'pib',
  location: 'us-central1',
}

export function createConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateConversation', inputVars)
}
createConversationRef.operationName = 'CreateConversation'

export function createConversation(dcOrVars, vars) {
  return executeMutation(createConversationRef(dcOrVars, vars))
}

export function createConversationWithSummaryRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateConversationWithSummary', inputVars)
}
createConversationWithSummaryRef.operationName = 'CreateConversationWithSummary'

export function createConversationWithSummary(dcOrVars, vars) {
  return executeMutation(createConversationWithSummaryRef(dcOrVars, vars))
}

export function addConversationParticipantRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AddConversationParticipant', inputVars)
}
addConversationParticipantRef.operationName = 'AddConversationParticipant'

export function addConversationParticipant(dcOrVars, vars) {
  return executeMutation(addConversationParticipantRef(dcOrVars, vars))
}

export function createMessageRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateMessage', inputVars)
}
createMessageRef.operationName = 'CreateMessage'

export function createMessage(dcOrVars, vars) {
  return executeMutation(createMessageRef(dcOrVars, vars))
}

export function createMessageWithEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateMessageWithEmbedding', inputVars)
}
createMessageWithEmbeddingRef.operationName = 'CreateMessageWithEmbedding'

export function createMessageWithEmbedding(dcOrVars, vars) {
  return executeMutation(createMessageWithEmbeddingRef(dcOrVars, vars))
}

export function updateConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateConversation', inputVars)
}
updateConversationRef.operationName = 'UpdateConversation'

export function updateConversation(dcOrVars, vars) {
  return executeMutation(updateConversationRef(dcOrVars, vars))
}

export function updateConversationWithSummaryRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateConversationWithSummary', inputVars)
}
updateConversationWithSummaryRef.operationName = 'UpdateConversationWithSummary'

export function updateConversationWithSummary(dcOrVars, vars) {
  return executeMutation(updateConversationWithSummaryRef(dcOrVars, vars))
}

export function markMessagesAsReadRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'MarkMessagesAsRead', inputVars)
}
markMessagesAsReadRef.operationName = 'MarkMessagesAsRead'

export function markMessagesAsRead(dcOrVars, vars) {
  return executeMutation(markMessagesAsReadRef(dcOrVars, vars))
}

export function addReactionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AddReaction', inputVars)
}
addReactionRef.operationName = 'AddReaction'

export function addReaction(dcOrVars, vars) {
  return executeMutation(addReactionRef(dcOrVars, vars))
}

export function removeReactionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RemoveReaction', inputVars)
}
removeReactionRef.operationName = 'RemoveReaction'

export function removeReaction(dcOrVars, vars) {
  return executeMutation(removeReactionRef(dcOrVars, vars))
}

export function updateConversationParticipantRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateConversationParticipant', inputVars)
}
updateConversationParticipantRef.operationName = 'UpdateConversationParticipant'

export function updateConversationParticipant(dcOrVars, vars) {
  return executeMutation(updateConversationParticipantRef(dcOrVars, vars))
}

export function deleteMessageRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteMessage', inputVars)
}
deleteMessageRef.operationName = 'DeleteMessage'

export function deleteMessage(dcOrVars, vars) {
  return executeMutation(deleteMessageRef(dcOrVars, vars))
}

export function editMessageRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'EditMessage', inputVars)
}
editMessageRef.operationName = 'EditMessage'

export function editMessage(dcOrVars, vars) {
  return executeMutation(editMessageRef(dcOrVars, vars))
}

export function editMessageWithEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'EditMessageWithEmbedding', inputVars)
}
editMessageWithEmbeddingRef.operationName = 'EditMessageWithEmbedding'

export function editMessageWithEmbedding(dcOrVars, vars) {
  return executeMutation(editMessageWithEmbeddingRef(dcOrVars, vars))
}

export function deleteConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteConversation', inputVars)
}
deleteConversationRef.operationName = 'DeleteConversation'

export function deleteConversation(dcOrVars, vars) {
  return executeMutation(deleteConversationRef(dcOrVars, vars))
}

export function leaveConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LeaveConversation', inputVars)
}
leaveConversationRef.operationName = 'LeaveConversation'

export function leaveConversation(dcOrVars, vars) {
  return executeMutation(leaveConversationRef(dcOrVars, vars))
}

export function removeConversationParticipantRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RemoveConversationParticipant', inputVars)
}
removeConversationParticipantRef.operationName = 'RemoveConversationParticipant'

export function removeConversationParticipant(dcOrVars, vars) {
  return executeMutation(removeConversationParticipantRef(dcOrVars, vars))
}

export function updateConversationAfterMessageRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateConversationAfterMessage', inputVars)
}
updateConversationAfterMessageRef.operationName = 'UpdateConversationAfterMessage'

export function updateConversationAfterMessage(dcOrVars, vars) {
  return executeMutation(updateConversationAfterMessageRef(dcOrVars, vars))
}

export function incrementUnreadCountsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'IncrementUnreadCounts', inputVars)
}
incrementUnreadCountsRef.operationName = 'IncrementUnreadCounts'

export function incrementUnreadCounts(dcOrVars, vars) {
  return executeMutation(incrementUnreadCountsRef(dcOrVars, vars))
}

export function createDirectConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateDirectConversation', inputVars)
}
createDirectConversationRef.operationName = 'CreateDirectConversation'

export function createDirectConversation(dcOrVars, vars) {
  return executeMutation(createDirectConversationRef(dcOrVars, vars))
}

export function addMultipleParticipantsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AddMultipleParticipants', inputVars)
}
addMultipleParticipantsRef.operationName = 'AddMultipleParticipants'

export function addMultipleParticipants(dcOrVars, vars) {
  return executeMutation(addMultipleParticipantsRef(dcOrVars, vars))
}

export function getConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversation', inputVars)
}
getConversationRef.operationName = 'GetConversation'

export function getConversation(dcOrVars, vars) {
  return executeQuery(getConversationRef(dcOrVars, vars))
}

export function getConversationWithParticipantsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversationWithParticipants', inputVars)
}
getConversationWithParticipantsRef.operationName = 'GetConversationWithParticipants'

export function getConversationWithParticipants(dcOrVars, vars) {
  return executeQuery(getConversationWithParticipantsRef(dcOrVars, vars))
}

export function getConversationMessagesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversationMessages', inputVars)
}
getConversationMessagesRef.operationName = 'GetConversationMessages'

export function getConversationMessages(dcOrVars, vars) {
  return executeQuery(getConversationMessagesRef(dcOrVars, vars))
}

export function getConversationMessagesWithReactionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversationMessagesWithReactions', inputVars)
}
getConversationMessagesWithReactionsRef.operationName = 'GetConversationMessagesWithReactions'

export function getConversationMessagesWithReactions(dcOrVars, vars) {
  return executeQuery(getConversationMessagesWithReactionsRef(dcOrVars, vars))
}

export function getUserConversationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserConversations', inputVars)
}
getUserConversationsRef.operationName = 'GetUserConversations'

export function getUserConversations(dcOrVars, vars) {
  return executeQuery(getUserConversationsRef(dcOrVars, vars))
}

export function getDirectConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetDirectConversation', inputVars)
}
getDirectConversationRef.operationName = 'GetDirectConversation'

export function getDirectConversation(dcOrVars, vars) {
  return executeQuery(getDirectConversationRef(dcOrVars, vars))
}

export function searchConversationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchConversations', inputVars)
}
searchConversationsRef.operationName = 'SearchConversations'

export function searchConversations(dcOrVars, vars) {
  return executeQuery(searchConversationsRef(dcOrVars, vars))
}

export function searchMessagesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchMessages', inputVars)
}
searchMessagesRef.operationName = 'SearchMessages'

export function searchMessages(dcOrVars, vars) {
  return executeQuery(searchMessagesRef(dcOrVars, vars))
}

export function searchWorkspaceMessagesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchWorkspaceMessages', inputVars)
}
searchWorkspaceMessagesRef.operationName = 'SearchWorkspaceMessages'

export function searchWorkspaceMessages(dcOrVars, vars) {
  return executeQuery(searchWorkspaceMessagesRef(dcOrVars, vars))
}

export function getUnreadCountsRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUnreadCounts')
}
getUnreadCountsRef.operationName = 'GetUnreadCounts'

export function getUnreadCounts(dc) {
  return executeQuery(getUnreadCountsRef(dc))
}

export function getTotalUnreadCountRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetTotalUnreadCount')
}
getTotalUnreadCountRef.operationName = 'GetTotalUnreadCount'

export function getTotalUnreadCount(dc) {
  return executeQuery(getTotalUnreadCountRef(dc))
}

export function getConversationParticipantRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConversationParticipant', inputVars)
}
getConversationParticipantRef.operationName = 'GetConversationParticipant'

export function getConversationParticipant(dcOrVars, vars) {
  return executeQuery(getConversationParticipantRef(dcOrVars, vars))
}

export function getMessageReactionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetMessageReactions', inputVars)
}
getMessageReactionsRef.operationName = 'GetMessageReactions'

export function getMessageReactions(dcOrVars, vars) {
  return executeQuery(getMessageReactionsRef(dcOrVars, vars))
}

export function getUserMessageReactionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserMessageReaction', inputVars)
}
getUserMessageReactionRef.operationName = 'GetUserMessageReaction'

export function getUserMessageReaction(dcOrVars, vars) {
  return executeQuery(getUserMessageReactionRef(dcOrVars, vars))
}

export function findDirectConversationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'FindDirectConversation', inputVars)
}
findDirectConversationRef.operationName = 'FindDirectConversation'

export function findDirectConversation(dcOrVars, vars) {
  return executeQuery(findDirectConversationRef(dcOrVars, vars))
}

export function getAiChatConversationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAIChatConversations', inputVars)
}
getAiChatConversationsRef.operationName = 'GetAIChatConversations'

export function getAiChatConversations(dcOrVars, vars) {
  return executeQuery(getAiChatConversationsRef(dcOrVars, vars))
}

export function createUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateUser', inputVars)
}
createUserRef.operationName = 'CreateUser'

export function createUser(dcOrVars, vars) {
  return executeMutation(createUserRef(dcOrVars, vars))
}

export function createUserForSignupRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateUserForSignup', inputVars)
}
createUserForSignupRef.operationName = 'CreateUserForSignup'

export function createUserForSignup(dcOrVars, vars) {
  return executeMutation(createUserForSignupRef(dcOrVars, vars))
}

export function updateUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateUser', inputVars)
}
updateUserRef.operationName = 'UpdateUser'

export function updateUser(dcOrVars, vars) {
  return executeMutation(updateUserRef(dcOrVars, vars))
}

export function createProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateProfile', inputVars)
}
createProfileRef.operationName = 'CreateProfile'

export function createProfile(dcOrVars, vars) {
  return executeMutation(createProfileRef(dcOrVars, vars))
}

export function createProfileForSignupRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateProfileForSignup', inputVars)
}
createProfileForSignupRef.operationName = 'CreateProfileForSignup'

export function createProfileForSignup(dcOrVars, vars) {
  return executeMutation(createProfileForSignupRef(dcOrVars, vars))
}

export function createProfileWithBioRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateProfileWithBio', inputVars)
}
createProfileWithBioRef.operationName = 'CreateProfileWithBio'

export function createProfileWithBio(dcOrVars, vars) {
  return executeMutation(createProfileWithBioRef(dcOrVars, vars))
}

export function updateProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateProfile', inputVars)
}
updateProfileRef.operationName = 'UpdateProfile'

export function updateProfile(dcOrVars, vars) {
  return executeMutation(updateProfileRef(dcOrVars, vars))
}

export function updateProfileWithBioRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateProfileWithBio', inputVars)
}
updateProfileWithBioRef.operationName = 'UpdateProfileWithBio'

export function updateProfileWithBio(dcOrVars, vars) {
  return executeMutation(updateProfileWithBioRef(dcOrVars, vars))
}

export function deleteProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteProfile', inputVars)
}
deleteProfileRef.operationName = 'DeleteProfile'

export function deleteProfile(dcOrVars, vars) {
  return executeMutation(deleteProfileRef(dcOrVars, vars))
}

export function createWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateWorkspace', inputVars)
}
createWorkspaceRef.operationName = 'CreateWorkspace'

export function createWorkspace(dcOrVars, vars) {
  return executeMutation(createWorkspaceRef(dcOrVars, vars))
}

export function createWorkspaceForSignupRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateWorkspaceForSignup', inputVars)
}
createWorkspaceForSignupRef.operationName = 'CreateWorkspaceForSignup'

export function createWorkspaceForSignup(dcOrVars, vars) {
  return executeMutation(createWorkspaceForSignupRef(dcOrVars, vars))
}

export function updateWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateWorkspace', inputVars)
}
updateWorkspaceRef.operationName = 'UpdateWorkspace'

export function updateWorkspace(dcOrVars, vars) {
  return executeMutation(updateWorkspaceRef(dcOrVars, vars))
}

export function deleteWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteWorkspace', inputVars)
}
deleteWorkspaceRef.operationName = 'DeleteWorkspace'

export function deleteWorkspace(dcOrVars, vars) {
  return executeMutation(deleteWorkspaceRef(dcOrVars, vars))
}

export function addWorkspaceMemberRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AddWorkspaceMember', inputVars)
}
addWorkspaceMemberRef.operationName = 'AddWorkspaceMember'

export function addWorkspaceMember(dcOrVars, vars) {
  return executeMutation(addWorkspaceMemberRef(dcOrVars, vars))
}

export function updateWorkspaceMemberRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateWorkspaceMember', inputVars)
}
updateWorkspaceMemberRef.operationName = 'UpdateWorkspaceMember'

export function updateWorkspaceMember(dcOrVars, vars) {
  return executeMutation(updateWorkspaceMemberRef(dcOrVars, vars))
}

export function removeWorkspaceMemberRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RemoveWorkspaceMember', inputVars)
}
removeWorkspaceMemberRef.operationName = 'RemoveWorkspaceMember'

export function removeWorkspaceMember(dcOrVars, vars) {
  return executeMutation(removeWorkspaceMemberRef(dcOrVars, vars))
}

export function inviteToWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'InviteToWorkspace', inputVars)
}
inviteToWorkspaceRef.operationName = 'InviteToWorkspace'

export function inviteToWorkspace(dcOrVars, vars) {
  return executeMutation(inviteToWorkspaceRef(dcOrVars, vars))
}

export function acceptInvitationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'AcceptInvitation', inputVars)
}
acceptInvitationRef.operationName = 'AcceptInvitation'

export function acceptInvitation(dcOrVars, vars) {
  return executeMutation(acceptInvitationRef(dcOrVars, vars))
}

export function declineInvitationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeclineInvitation', inputVars)
}
declineInvitationRef.operationName = 'DeclineInvitation'

export function declineInvitation(dcOrVars, vars) {
  return executeMutation(declineInvitationRef(dcOrVars, vars))
}

export function createBusinessProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateBusinessProfile', inputVars)
}
createBusinessProfileRef.operationName = 'CreateBusinessProfile'

export function createBusinessProfile(dcOrVars, vars) {
  return executeMutation(createBusinessProfileRef(dcOrVars, vars))
}

export function createBusinessProfileWithDescriptionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateBusinessProfileWithDescription', inputVars)
}
createBusinessProfileWithDescriptionRef.operationName = 'CreateBusinessProfileWithDescription'

export function createBusinessProfileWithDescription(dcOrVars, vars) {
  return executeMutation(createBusinessProfileWithDescriptionRef(dcOrVars, vars))
}

export function updateBusinessProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateBusinessProfile', inputVars)
}
updateBusinessProfileRef.operationName = 'UpdateBusinessProfile'

export function updateBusinessProfile(dcOrVars, vars) {
  return executeMutation(updateBusinessProfileRef(dcOrVars, vars))
}

export function updateBusinessProfileWithDescriptionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateBusinessProfileWithDescription', inputVars)
}
updateBusinessProfileWithDescriptionRef.operationName = 'UpdateBusinessProfileWithDescription'

export function updateBusinessProfileWithDescription(dcOrVars, vars) {
  return executeMutation(updateBusinessProfileWithDescriptionRef(dcOrVars, vars))
}

export function createPartnerPreferencesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreatePartnerPreferences', inputVars)
}
createPartnerPreferencesRef.operationName = 'CreatePartnerPreferences'

export function createPartnerPreferences(dcOrVars, vars) {
  return executeMutation(createPartnerPreferencesRef(dcOrVars, vars))
}

export function createPartnerPreferencesWithEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreatePartnerPreferencesWithEmbedding', inputVars)
}
createPartnerPreferencesWithEmbeddingRef.operationName = 'CreatePartnerPreferencesWithEmbedding'

export function createPartnerPreferencesWithEmbedding(dcOrVars, vars) {
  return executeMutation(createPartnerPreferencesWithEmbeddingRef(dcOrVars, vars))
}

export function updatePartnerPreferencesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdatePartnerPreferences', inputVars)
}
updatePartnerPreferencesRef.operationName = 'UpdatePartnerPreferences'

export function updatePartnerPreferences(dcOrVars, vars) {
  return executeMutation(updatePartnerPreferencesRef(dcOrVars, vars))
}

export function updatePartnerPreferencesWithEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdatePartnerPreferencesWithEmbedding', inputVars)
}
updatePartnerPreferencesWithEmbeddingRef.operationName = 'UpdatePartnerPreferencesWithEmbedding'

export function updatePartnerPreferencesWithEmbedding(dcOrVars, vars) {
  return executeMutation(updatePartnerPreferencesWithEmbeddingRef(dcOrVars, vars))
}

export function createLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateLLMKey', inputVars)
}
createLlmKeyRef.operationName = 'CreateLLMKey'

export function createLlmKey(dcOrVars, vars) {
  return executeMutation(createLlmKeyRef(dcOrVars, vars))
}

export function updateLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateLLMKey', inputVars)
}
updateLlmKeyRef.operationName = 'UpdateLLMKey'

export function updateLlmKey(dcOrVars, vars) {
  return executeMutation(updateLlmKeyRef(dcOrVars, vars))
}

export function deleteLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteLLMKey', inputVars)
}
deleteLlmKeyRef.operationName = 'DeleteLLMKey'

export function deleteLlmKey(dcOrVars, vars) {
  return executeMutation(deleteLlmKeyRef(dcOrVars, vars))
}

export function createAuditLogRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateAuditLog', inputVars)
}
createAuditLogRef.operationName = 'CreateAuditLog'

export function createAuditLog(dcOrVars, vars) {
  return executeMutation(createAuditLogRef(dcOrVars, vars))
}

export function createUserSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateUserSession', inputVars)
}
createUserSessionRef.operationName = 'CreateUserSession'

export function createUserSession(dcOrVars, vars) {
  return executeMutation(createUserSessionRef(dcOrVars, vars))
}

export function updateUserSessionActivityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateUserSessionActivity', inputVars)
}
updateUserSessionActivityRef.operationName = 'UpdateUserSessionActivity'

export function updateUserSessionActivity(dcOrVars, vars) {
  return executeMutation(updateUserSessionActivityRef(dcOrVars, vars))
}

export function revokeUserSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RevokeUserSession', inputVars)
}
revokeUserSessionRef.operationName = 'RevokeUserSession'

export function revokeUserSession(dcOrVars, vars) {
  return executeMutation(revokeUserSessionRef(dcOrVars, vars))
}

export function deleteUserSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteUserSession', inputVars)
}
deleteUserSessionRef.operationName = 'DeleteUserSession'

export function deleteUserSession(dcOrVars, vars) {
  return executeMutation(deleteUserSessionRef(dcOrVars, vars))
}

export function createAnalyticsEventRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateAnalyticsEvent', inputVars)
}
createAnalyticsEventRef.operationName = 'CreateAnalyticsEvent'

export function createAnalyticsEvent(dcOrVars, vars) {
  return executeMutation(createAnalyticsEventRef(dcOrVars, vars))
}

export function createEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateEmbedding', inputVars)
}
createEmbeddingRef.operationName = 'CreateEmbedding'

export function createEmbedding(dcOrVars, vars) {
  return executeMutation(createEmbeddingRef(dcOrVars, vars))
}

export function updateEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'UpdateEmbedding', inputVars)
}
updateEmbeddingRef.operationName = 'UpdateEmbedding'

export function updateEmbedding(dcOrVars, vars) {
  return executeMutation(updateEmbeddingRef(dcOrVars, vars))
}

export function softDeleteEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'SoftDeleteEmbedding', inputVars)
}
softDeleteEmbeddingRef.operationName = 'SoftDeleteEmbedding'

export function softDeleteEmbedding(dcOrVars, vars) {
  return executeMutation(softDeleteEmbeddingRef(dcOrVars, vars))
}

export function deleteEmbeddingRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'DeleteEmbedding', inputVars)
}
deleteEmbeddingRef.operationName = 'DeleteEmbedding'

export function deleteEmbedding(dcOrVars, vars) {
  return executeMutation(deleteEmbeddingRef(dcOrVars, vars))
}

export function createEmbeddingBatchRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateEmbeddingBatch', inputVars)
}
createEmbeddingBatchRef.operationName = 'CreateEmbeddingBatch'

export function createEmbeddingBatch(dcOrVars, vars) {
  return executeMutation(createEmbeddingBatchRef(dcOrVars, vars))
}

export function getAllUsersRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAllUsers')
}
getAllUsersRef.operationName = 'GetAllUsers'

export function getAllUsers(dc) {
  return executeQuery(getAllUsersRef(dc))
}

export function getCurrentUserRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetCurrentUser')
}
getCurrentUserRef.operationName = 'GetCurrentUser'

export function getCurrentUser(dc) {
  return executeQuery(getCurrentUserRef(dc))
}

export function searchProfilesByBioRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchProfilesByBio', inputVars)
}
searchProfilesByBioRef.operationName = 'SearchProfilesByBio'

export function searchProfilesByBio(dcOrVars, vars) {
  return executeQuery(searchProfilesByBioRef(dcOrVars, vars))
}

export function searchBusinessProfilesByDescriptionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchBusinessProfilesByDescription', inputVars)
}
searchBusinessProfilesByDescriptionRef.operationName = 'SearchBusinessProfilesByDescription'

export function searchBusinessProfilesByDescription(dcOrVars, vars) {
  return executeQuery(searchBusinessProfilesByDescriptionRef(dcOrVars, vars))
}

export function matchProfileToBusinessesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'MatchProfileToBusinesses', inputVars)
}
matchProfileToBusinessesRef.operationName = 'MatchProfileToBusinesses'

export function matchProfileToBusinesses(dcOrVars, vars) {
  return executeQuery(matchProfileToBusinessesRef(dcOrVars, vars))
}

export function businessProfilesBySimilarityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'BusinessProfilesBySimilarity', inputVars)
}
businessProfilesBySimilarityRef.operationName = 'BusinessProfilesBySimilarity'

export function businessProfilesBySimilarity(dcOrVars, vars) {
  return executeQuery(businessProfilesBySimilarityRef(dcOrVars, vars))
}

export function matchBusinessToProfilesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'MatchBusinessToProfiles', inputVars)
}
matchBusinessToProfilesRef.operationName = 'MatchBusinessToProfiles'

export function matchBusinessToProfiles(dcOrVars, vars) {
  return executeQuery(matchBusinessToProfilesRef(dcOrVars, vars))
}

export function profilesBySimilarityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'ProfilesBySimilarity', inputVars)
}
profilesBySimilarityRef.operationName = 'ProfilesBySimilarity'

export function profilesBySimilarity(dcOrVars, vars) {
  return executeQuery(profilesBySimilarityRef(dcOrVars, vars))
}

export function searchPartnerPreferencesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchPartnerPreferences', inputVars)
}
searchPartnerPreferencesRef.operationName = 'SearchPartnerPreferences'

export function searchPartnerPreferences(dcOrVars, vars) {
  return executeQuery(searchPartnerPreferencesRef(dcOrVars, vars))
}

export function getUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUser', inputVars)
}
getUserRef.operationName = 'GetUser'

export function getUser(dcOrVars, vars) {
  return executeQuery(getUserRef(dcOrVars, vars))
}

export function getUserProfilesRef(dc) {
  const { dc: dcInstance } = validateArgs(connectorConfig, dc, undefined)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserProfiles')
}
getUserProfilesRef.operationName = 'GetUserProfiles'

export function getUserProfiles(dc) {
  return executeQuery(getUserProfilesRef(dc))
}

export function getUserWorkspacesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserWorkspaces', inputVars)
}
getUserWorkspacesRef.operationName = 'GetUserWorkspaces'

export function getUserWorkspaces(dcOrVars, vars) {
  return executeQuery(getUserWorkspacesRef(dcOrVars, vars))
}

export function getWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspace', inputVars)
}
getWorkspaceRef.operationName = 'GetWorkspace'

export function getWorkspace(dcOrVars, vars) {
  return executeQuery(getWorkspaceRef(dcOrVars, vars))
}

export function getWorkspaceMembersRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspaceMembers', inputVars)
}
getWorkspaceMembersRef.operationName = 'GetWorkspaceMembers'

export function getWorkspaceMembers(dcOrVars, vars) {
  return executeQuery(getWorkspaceMembersRef(dcOrVars, vars))
}

export function getBusinessProfileRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetBusinessProfile', inputVars)
}
getBusinessProfileRef.operationName = 'GetBusinessProfile'

export function getBusinessProfile(dcOrVars, vars) {
  return executeQuery(getBusinessProfileRef(dcOrVars, vars))
}

export function getPartnerPreferencesRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetPartnerPreferences', inputVars)
}
getPartnerPreferencesRef.operationName = 'GetPartnerPreferences'

export function getPartnerPreferences(dcOrVars, vars) {
  return executeQuery(getPartnerPreferencesRef(dcOrVars, vars))
}

export function getWorkspaceInvitationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspaceInvitations', inputVars)
}
getWorkspaceInvitationsRef.operationName = 'GetWorkspaceInvitations'

export function getWorkspaceInvitations(dcOrVars, vars) {
  return executeQuery(getWorkspaceInvitationsRef(dcOrVars, vars))
}

export function getPendingInvitationsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetPendingInvitations', inputVars)
}
getPendingInvitationsRef.operationName = 'GetPendingInvitations'

export function getPendingInvitations(dcOrVars, vars) {
  return executeQuery(getPendingInvitationsRef(dcOrVars, vars))
}

export function getLlmKeysByEntityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetLLMKeysByEntity', inputVars)
}
getLlmKeysByEntityRef.operationName = 'GetLLMKeysByEntity'

export function getLlmKeysByEntity(dcOrVars, vars) {
  return executeQuery(getLlmKeysByEntityRef(dcOrVars, vars))
}

export function getLlmKeyByProviderAndEntityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetLLMKeyByProviderAndEntity', inputVars)
}
getLlmKeyByProviderAndEntityRef.operationName = 'GetLLMKeyByProviderAndEntity'

export function getLlmKeyByProviderAndEntity(dcOrVars, vars) {
  return executeQuery(getLlmKeyByProviderAndEntityRef(dcOrVars, vars))
}

export function getWorkspaceLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspaceLLMKey', inputVars)
}
getWorkspaceLlmKeyRef.operationName = 'GetWorkspaceLLMKey'

export function getWorkspaceLlmKey(dcOrVars, vars) {
  return executeQuery(getWorkspaceLlmKeyRef(dcOrVars, vars))
}

export function getUserLlmKeyRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserLLMKey', inputVars)
}
getUserLlmKeyRef.operationName = 'GetUserLLMKey'

export function getUserLlmKey(dcOrVars, vars) {
  return executeQuery(getUserLlmKeyRef(dcOrVars, vars))
}

export function getProfileLlmKeysRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetProfileLLMKeys', inputVars)
}
getProfileLlmKeysRef.operationName = 'GetProfileLLMKeys'

export function getProfileLlmKeys(dcOrVars, vars) {
  return executeQuery(getProfileLlmKeysRef(dcOrVars, vars))
}

export function getAuditLogsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAuditLogs', inputVars)
}
getAuditLogsRef.operationName = 'GetAuditLogs'

export function getAuditLogs(dcOrVars, vars) {
  return executeQuery(getAuditLogsRef(dcOrVars, vars))
}

export function getAuditLogsByUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAuditLogsByUser', inputVars)
}
getAuditLogsByUserRef.operationName = 'GetAuditLogsByUser'

export function getAuditLogsByUser(dcOrVars, vars) {
  return executeQuery(getAuditLogsByUserRef(dcOrVars, vars))
}

export function getAuditLogsByWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAuditLogsByWorkspace', inputVars)
}
getAuditLogsByWorkspaceRef.operationName = 'GetAuditLogsByWorkspace'

export function getAuditLogsByWorkspace(dcOrVars, vars) {
  return executeQuery(getAuditLogsByWorkspaceRef(dcOrVars, vars))
}

export function getAuditLogsBySeverityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetAuditLogsBySeverity', inputVars)
}
getAuditLogsBySeverityRef.operationName = 'GetAuditLogsBySeverity'

export function getAuditLogsBySeverity(dcOrVars, vars) {
  return executeQuery(getAuditLogsBySeverityRef(dcOrVars, vars))
}

export function getActiveSessionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetActiveSessions', inputVars)
}
getActiveSessionsRef.operationName = 'GetActiveSessions'

export function getActiveSessions(dcOrVars, vars) {
  return executeQuery(getActiveSessionsRef(dcOrVars, vars))
}

export function getSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetSession', inputVars)
}
getSessionRef.operationName = 'GetSession'

export function getSession(dcOrVars, vars) {
  return executeQuery(getSessionRef(dcOrVars, vars))
}

export function getUserSessionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUserSessions', inputVars)
}
getUserSessionsRef.operationName = 'GetUserSessions'

export function getUserSessions(dcOrVars, vars) {
  return executeQuery(getUserSessionsRef(dcOrVars, vars))
}

export function getEmbeddingsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetEmbeddings', inputVars)
}
getEmbeddingsRef.operationName = 'GetEmbeddings'

export function getEmbeddings(dcOrVars, vars) {
  return executeQuery(getEmbeddingsRef(dcOrVars, vars))
}

export function getEmbeddingByEntityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetEmbeddingByEntity', inputVars)
}
getEmbeddingByEntityRef.operationName = 'GetEmbeddingByEntity'

export function getEmbeddingByEntity(dcOrVars, vars) {
  return executeQuery(getEmbeddingByEntityRef(dcOrVars, vars))
}

export function searchEmbeddingsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchEmbeddings', inputVars)
}
searchEmbeddingsRef.operationName = 'SearchEmbeddings'

export function searchEmbeddings(dcOrVars, vars) {
  return executeQuery(searchEmbeddingsRef(dcOrVars, vars))
}

export function createUploadRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateUpload', inputVars)
}
createUploadRef.operationName = 'CreateUpload'

export function createUpload(dcOrVars, vars) {
  return executeMutation(createUploadRef(dcOrVars, vars))
}

export function linkUserPhotoUploadRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LinkUserPhotoUpload', inputVars)
}
linkUserPhotoUploadRef.operationName = 'LinkUserPhotoUpload'

export function linkUserPhotoUpload(dcOrVars, vars) {
  return executeMutation(linkUserPhotoUploadRef(dcOrVars, vars))
}

export function listUploadsByWorkspaceRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'ListUploadsByWorkspace', inputVars)
}
listUploadsByWorkspaceRef.operationName = 'ListUploadsByWorkspace'

export function listUploadsByWorkspace(dcOrVars, vars) {
  return executeQuery(listUploadsByWorkspaceRef(dcOrVars, vars))
}

export function getUploadRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetUpload', inputVars)
}
getUploadRef.operationName = 'GetUpload'

export function getUpload(dcOrVars, vars) {
  return executeQuery(getUploadRef(dcOrVars, vars))
}

export function logSecurityEventRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogSecurityEvent', inputVars)
}
logSecurityEventRef.operationName = 'LogSecurityEvent'

export function logSecurityEvent(dcOrVars, vars) {
  return executeMutation(logSecurityEventRef(dcOrVars, vars))
}

export function logAuthenticationEventRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogAuthenticationEvent', inputVars)
}
logAuthenticationEventRef.operationName = 'LogAuthenticationEvent'

export function logAuthenticationEvent(dcOrVars, vars) {
  return executeMutation(logAuthenticationEventRef(dcOrVars, vars))
}

export function logFailedAuthenticationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogFailedAuthentication', inputVars)
}
logFailedAuthenticationRef.operationName = 'LogFailedAuthentication'

export function logFailedAuthentication(dcOrVars, vars) {
  return executeMutation(logFailedAuthenticationRef(dcOrVars, vars))
}

export function logDataAccessRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogDataAccess', inputVars)
}
logDataAccessRef.operationName = 'LogDataAccess'

export function logDataAccess(dcOrVars, vars) {
  return executeMutation(logDataAccessRef(dcOrVars, vars))
}

export function logPermissionChangeRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogPermissionChange', inputVars)
}
logPermissionChangeRef.operationName = 'LogPermissionChange'

export function logPermissionChange(dcOrVars, vars) {
  return executeMutation(logPermissionChangeRef(dcOrVars, vars))
}

export function createSecureSessionRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'CreateSecureSession', inputVars)
}
createSecureSessionRef.operationName = 'CreateSecureSession'

export function createSecureSession(dcOrVars, vars) {
  return executeMutation(createSecureSessionRef(dcOrVars, vars))
}

export function revokeSessionWithAuditRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'RevokeSessionWithAudit', inputVars)
}
revokeSessionWithAuditRef.operationName = 'RevokeSessionWithAudit'

export function revokeSessionWithAudit(dcOrVars, vars) {
  return executeMutation(revokeSessionWithAuditRef(dcOrVars, vars))
}

export function logBatchDataOperationRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogBatchDataOperation', inputVars)
}
logBatchDataOperationRef.operationName = 'LogBatchDataOperation'

export function logBatchDataOperation(dcOrVars, vars) {
  return executeMutation(logBatchDataOperationRef(dcOrVars, vars))
}

export function logWorkspaceEventRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'LogWorkspaceEvent', inputVars)
}
logWorkspaceEventRef.operationName = 'LogWorkspaceEvent'

export function logWorkspaceEvent(dcOrVars, vars) {
  return executeMutation(logWorkspaceEventRef(dcOrVars, vars))
}

export function emergencyRevokeAllUserSessionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return mutationRef(dcInstance, 'EmergencyRevokeAllUserSessions', inputVars)
}
emergencyRevokeAllUserSessionsRef.operationName = 'EmergencyRevokeAllUserSessions'

export function emergencyRevokeAllUserSessions(dcOrVars, vars) {
  return executeMutation(emergencyRevokeAllUserSessionsRef(dcOrVars, vars))
}

export function searchAuditLogsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'SearchAuditLogs', inputVars)
}
searchAuditLogsRef.operationName = 'SearchAuditLogs'

export function searchAuditLogs(dcOrVars, vars) {
  return executeQuery(searchAuditLogsRef(dcOrVars, vars))
}

export function getFailedLoginAttemptsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetFailedLoginAttempts', inputVars)
}
getFailedLoginAttemptsRef.operationName = 'GetFailedLoginAttempts'

export function getFailedLoginAttempts(dcOrVars, vars) {
  return executeQuery(getFailedLoginAttemptsRef(dcOrVars, vars))
}

export function getSuspiciousActivityRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetSuspiciousActivity', inputVars)
}
getSuspiciousActivityRef.operationName = 'GetSuspiciousActivity'

export function getSuspiciousActivity(dcOrVars, vars) {
  return executeQuery(getSuspiciousActivityRef(dcOrVars, vars))
}

export function getActiveSessionsForUserRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetActiveSessionsForUser', inputVars)
}
getActiveSessionsForUserRef.operationName = 'GetActiveSessionsForUser'

export function getActiveSessionsForUser(dcOrVars, vars) {
  return executeQuery(getActiveSessionsForUserRef(dcOrVars, vars))
}

export function getConcurrentSessionsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetConcurrentSessions', inputVars)
}
getConcurrentSessionsRef.operationName = 'GetConcurrentSessions'

export function getConcurrentSessions(dcOrVars, vars) {
  return executeQuery(getConcurrentSessionsRef(dcOrVars, vars))
}

export function getSessionsByIpRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetSessionsByIP', inputVars)
}
getSessionsByIpRef.operationName = 'GetSessionsByIP'

export function getSessionsByIp(dcOrVars, vars) {
  return executeQuery(getSessionsByIpRef(dcOrVars, vars))
}

export function getWorkspaceSecurityEventsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetWorkspaceSecurityEvents', inputVars)
}
getWorkspaceSecurityEventsRef.operationName = 'GetWorkspaceSecurityEvents'

export function getWorkspaceSecurityEvents(dcOrVars, vars) {
  return executeQuery(getWorkspaceSecurityEventsRef(dcOrVars, vars))
}

export function getDataAccessLogsRef(dcOrVars, vars) {
  const { dc: dcInstance, vars: inputVars } = validateArgs(connectorConfig, dcOrVars, vars, true)
  dcInstance._useGeneratedSdk()
  return queryRef(dcInstance, 'GetDataAccessLogs', inputVars)
}
getDataAccessLogsRef.operationName = 'GetDataAccessLogs'

export function getDataAccessLogs(dcOrVars, vars) {
  return executeQuery(getDataAccessLogsRef(dcOrVars, vars))
}
