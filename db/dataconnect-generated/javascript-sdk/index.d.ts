import type { Connector<PERSON>onfig, <PERSON>Connect, Mu<PERSON><PERSON><PERSON><PERSON>, Mutation<PERSON><PERSON>, Query<PERSON>romise, QueryRef } from 'firebase/data-connect'

export const connectorConfig: ConnectorConfig

export type TimestampString = string
export type UUIDString = string
export type Int64String = string
export type DateString = string

export interface AcceptInvitationData {
  acceptInvitation?: WorkspaceInvitation_Key | null
}

export interface AcceptInvitationVariables {
  invitationId: UUIDString
}

export interface Account_Key {
  id: UUIDString
  __typename?: 'Account_Key'
}

export interface AddConversationParticipantData {
  addParticipant: ConversationParticipant_Key
}

export interface AddConversationParticipantVariables {
  conversationId: UUIDString
  userId: string
}

export interface AddMultipleParticipantsData {
  addParticipant: ConversationParticipant_Key
}

export interface AddMultipleParticipantsVariables {
  conversationId: UUIDString
}

export interface AddReactionData {
  addReaction: MessageReaction_Key
}

export interface AddReactionVariables {
  messageId: UUIDString
  reaction: string
}

export interface AddWorkspaceMemberData {
  addWorkspaceMember: WorkspaceMember_Key
}

export interface AddWorkspaceMemberVariables {
  workspaceId: UUIDString
  userId: string
  profileId?: UUIDString | null
  role: string
}

export interface Address_Key {
  id: UUIDString
  __typename?: 'Address_Key'
}

export interface Agency_Key {
  uid: UUIDString
  __typename?: 'Agency_Key'
}

export interface Analytics_Key {
  id: UUIDString
  __typename?: 'Analytics_Key'
}

export interface AuditLog_Key {
  id: UUIDString
  __typename?: 'AuditLog_Key'
}

export interface BusinessProfile_Key {
  id: UUIDString
  __typename?: 'BusinessProfile_Key'
}

export interface BusinessProfilesBySimilarityData {
  businessProfiles_descriptionEmbedding_similarity: ({
    id: UUIDString
    workspaceId: UUIDString
    name: string
    industry?: string | null
    description?: string | null
    location?: string | null
    website?: string | null
    employeeCount?: number | null
  } & BusinessProfile_Key)[]
}

export interface BusinessProfilesBySimilarityVariables {
  bioText: string
  limit?: number | null
}

export interface CRMContactAddress_Key {
  contactId: UUIDString
  addressId: UUIDString
  __typename?: 'CRMContactAddress_Key'
}

export interface CRMContact_Key {
  id: UUIDString
  __typename?: 'CRMContact_Key'
}

export interface CardAddress_Key {
  cardId: UUIDString
  addressId: UUIDString
  __typename?: 'CardAddress_Key'
}

export interface Card_Key {
  id: UUIDString
  __typename?: 'Card_Key'
}

export interface CompanyAddress_Key {
  companyId: UUIDString
  addressId: UUIDString
  __typename?: 'CompanyAddress_Key'
}

export interface Company_Key {
  id: UUIDString
  __typename?: 'Company_Key'
}

export interface ConversationParticipant_Key {
  id: UUIDString
  __typename?: 'ConversationParticipant_Key'
}

export interface Conversation_Key {
  id: UUIDString
  __typename?: 'Conversation_Key'
}

export interface Course_Key {
  id: UUIDString
  __typename?: 'Course_Key'
}

export interface CreateAnalyticsEventData {
  createAnalyticsEvent: Analytics_Key
}

export interface CreateAnalyticsEventVariables {
  workspaceId?: UUIDString | null
  userId?: string | null
  eventType: string
  eventName: string
  category?: string | null
  properties?: string | null
  value?: number | null
  sessionId?: string | null
  ipAddress?: string | null
  userAgent?: string | null
  referrer?: string | null
}

export interface CreateAuditLogData {
  createAuditLog: AuditLog_Key
}

export interface CreateAuditLogVariables {
  workspaceId?: UUIDString | null
  userId?: string | null
  action: string
  resource?: string | null
  resourceId?: string | null
  ipAddress?: string | null
  userAgent?: string | null
  severity?: string | null
  status?: string | null
  metadata?: string | null
  sessionId?: string | null
}

export interface CreateBusinessProfileData {
  createBusinessProfile: BusinessProfile_Key
}

export interface CreateBusinessProfileVariables {
  workspaceId: UUIDString
  name: string
  industry?: string | null
  description?: string | null
  location?: string | null
  website?: string | null
  employeeCount?: number | null
}

export interface CreateBusinessProfileWithDescriptionData {
  createBusinessProfile: BusinessProfile_Key
}

export interface CreateBusinessProfileWithDescriptionVariables {
  workspaceId: UUIDString
  name: string
  industry?: string | null
  description: string
  location?: string | null
  website?: string | null
  employeeCount?: number | null
}

export interface CreateConversationData {
  createConversation: Conversation_Key
}

export interface CreateConversationVariables {
  workspaceId: UUIDString
  title?: string | null
  summary?: string | null
  type: string
  metadata?: string | null
}

export interface CreateConversationWithSummaryData {
  createConversation: Conversation_Key
}

export interface CreateConversationWithSummaryVariables {
  workspaceId: UUIDString
  title?: string | null
  summary: string
  type: string
  metadata?: string | null
}

export interface CreateDirectConversationData {
  createConversation: Conversation_Key
}

export interface CreateDirectConversationVariables {
  workspaceId: UUIDString
  title?: string | null
}

export interface CreateEmbeddingBatchData {
  deactivateOldEmbeddings: number
  createNewEmbedding: Embedding_Key
}

export interface CreateEmbeddingBatchVariables {
  workspaceId: UUIDString
  entityType: string
  entityId: string
  contentHash: string
  content: string
  model: string
  tokenCount?: number | null
  metadata?: string | null
}

export interface CreateEmbeddingData {
  createEmbedding: Embedding_Key
}

export interface CreateEmbeddingVariables {
  workspaceId: UUIDString
  entityType: string
  entityId: string
  contentHash: string
  content: string
  model: string
  tokenCount?: number | null
  metadata?: string | null
  version?: number | null
}

export interface CreateLlmKeyData {
  createLLMKey: LlmKey_Key
}

export interface CreateLlmKeyVariables {
  entityType: string
  entityId: string
  provider: string
  encryptedKey?: string | null
  encryptionIV?: string | null
  config?: string | null
}

export interface CreateMessageData {
  createMessage: Message_Key
}

export interface CreateMessageVariables {
  conversationId: UUIDString
  content: string
  role: string
  parentMessageId?: UUIDString | null
  metadata?: string | null
}

export interface CreateMessageWithEmbeddingData {
  createMessage: Message_Key
}

export interface CreateMessageWithEmbeddingVariables {
  conversationId: UUIDString
  content: string
  role: string
  parentMessageId?: UUIDString | null
  metadata?: string | null
}

export interface CreatePartnerPreferencesData {
  createPartnerPreferences: PartnerPreferences_Key
}

export interface CreatePartnerPreferencesVariables {
  workspaceId: UUIDString
  industries?: string[] | null
  locations?: string[] | null
  minEmployeeCount?: number | null
  maxEmployeeCount?: number | null
  skillsNeeded?: string[] | null
}

export interface CreatePartnerPreferencesWithEmbeddingData {
  createPartnerPreferences: PartnerPreferences_Key
}

export interface CreatePartnerPreferencesWithEmbeddingVariables {
  workspaceId: UUIDString
  industries?: string[] | null
  locations?: string[] | null
  minEmployeeCount?: number | null
  maxEmployeeCount?: number | null
  skillsNeeded?: string[] | null
  combinedText: string
}

export interface CreateProfileData {
  createProfile: Profile_Key
}

export interface CreateProfileForSignupData {
  createProfile: Profile_Key
}

export interface CreateProfileForSignupVariables {
  userId: string
  name: string
  bio?: string | null
  avatarUrl?: string | null
  skills?: string[] | null
  interests?: string[] | null
  isDefault?: boolean | null
}

export interface CreateProfileVariables {
  name: string
  bio?: string | null
  avatarUrl?: string | null
  skills?: string[] | null
  interests?: string[] | null
  isDefault?: boolean | null
}

export interface CreateProfileWithBioData {
  createProfile: Profile_Key
}

export interface CreateProfileWithBioVariables {
  name: string
  bio: string
  avatarUrl?: string | null
  skills?: string[] | null
  interests?: string[] | null
  isDefault?: boolean | null
}

export interface CreateSecureSessionData {
  createSession: UserSession_Key
  logSessionCreation: AuditLog_Key
}

export interface CreateSecureSessionVariables {
  id: string
  userId: string
  workspaceId?: UUIDString | null
  ipAddress: string
  userAgent: string
  deviceType?: string | null
  expiresAt: TimestampString
  metadata?: string | null
}

export interface CreateUploadData {
  createUpload: Upload_Key
}

export interface CreateUploadVariables {
  workspaceId: UUIDString
  uploadedBy?: string | null
  name: string
  src: string
  contentType?: string | null
}

export interface CreateUserData {
  createUser: User_Key
}

export interface CreateUserForSignupData {
  createUser: User_Key
}

export interface CreateUserForSignupVariables {
  uid: string
  email: string
  displayName?: string | null
  photoUrl?: string | null
}

export interface CreateUserSessionData {
  createUserSession: UserSession_Key
}

export interface CreateUserSessionVariables {
  id: string
  userId: string
  workspaceId?: UUIDString | null
  ipAddress?: string | null
  userAgent?: string | null
  deviceType?: string | null
  expiresAt: TimestampString
  metadata?: string | null
}

export interface CreateUserVariables {
  email: string
  displayName?: string | null
  photoUrl?: string | null
}

export interface CreateWorkspaceData {
  createWorkspace: Workspace_Key
}

export interface CreateWorkspaceForSignupData {
  createWorkspace: Workspace_Key
}

export interface CreateWorkspaceForSignupVariables {
  name: string
  description?: string | null
  logoUrl?: string | null
  ownerId: string
}

export interface CreateWorkspaceVariables {
  name: string
  description?: string | null
  logoUrl?: string | null
}

export interface DeclineInvitationData {
  declineInvitation?: WorkspaceInvitation_Key | null
}

export interface DeclineInvitationVariables {
  invitationId: UUIDString
}

export interface DeleteConversationData {
  deleteConversation?: Conversation_Key | null
}

export interface DeleteConversationVariables {
  conversationId: UUIDString
}

export interface DeleteEmbeddingData {
  deleteEmbedding?: Embedding_Key | null
}

export interface DeleteEmbeddingVariables {
  id: UUIDString
}

export interface DeleteLlmKeyData {
  deleteLLMKey?: LlmKey_Key | null
}

export interface DeleteLlmKeyVariables {
  id: UUIDString
}

export interface DeleteMessageData {
  deleteMessage?: Message_Key | null
}

export interface DeleteMessageVariables {
  messageId: UUIDString
}

export interface DeleteProfileData {
  deleteProfile?: Profile_Key | null
}

export interface DeleteProfileVariables {
  id: UUIDString
}

export interface DeleteUserSessionData {
  deleteUserSession?: UserSession_Key | null
}

export interface DeleteUserSessionVariables {
  id: string
}

export interface DeleteWorkspaceData {
  deleteWorkspace?: Workspace_Key | null
}

export interface DeleteWorkspaceVariables {
  id: UUIDString
}

export interface Document_Key {
  id: UUIDString
  __typename?: 'Document_Key'
}

export interface EditMessageData {
  editMessage?: Message_Key | null
}

export interface EditMessageVariables {
  messageId: UUIDString
  content: string
}

export interface EditMessageWithEmbeddingData {
  editMessage?: Message_Key | null
}

export interface EditMessageWithEmbeddingVariables {
  messageId: UUIDString
  content: string
}

export interface Embedding_Key {
  id: UUIDString
  __typename?: 'Embedding_Key'
}

export interface EmergencyRevokeAllUserSessionsData {
  revokeAllSessions: number
  logEmergencyAction: AuditLog_Key
}

export interface EmergencyRevokeAllUserSessionsVariables {
  userId: string
  revokedBy: string
  reason: string
  ipAddress?: string | null
}

export interface FindDirectConversationData {
  conversations: ({
    id: UUIDString
    title?: string | null
    type: string
    lastMessageAt?: TimestampString | null
  } & Conversation_Key)[]
}

export interface FindDirectConversationVariables {
  workspaceId: UUIDString
}

export interface GetActiveSessionsData {
  userSessions: ({
    id: string
    userId: string
    workspaceId?: UUIDString | null
    ipAddress?: string | null
    userAgent?: string | null
    deviceType?: string | null
    isActive: boolean
    lastActivityAt: TimestampString
    expiresAt: TimestampString
    metadata?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & UserSession_Key)[]
}

export interface GetActiveSessionsForUserData {
  userSessions: ({
    id: string
    userId: string
    workspaceId?: UUIDString | null
    ipAddress?: string | null
    userAgent?: string | null
    deviceType?: string | null
    lastActivityAt: TimestampString
    expiresAt: TimestampString
    createdAt: TimestampString
  } & UserSession_Key)[]
}

export interface GetActiveSessionsForUserVariables {
  userId: string
}

export interface GetActiveSessionsVariables {
  userId: string
}

export interface GetAiChatConversationsData {
  conversations: ({
    id: UUIDString
    title?: string | null
    summary?: string | null
    lastMessageAt?: TimestampString | null
    messageCount?: number | null
    createdAt: TimestampString
  } & Conversation_Key)[]
}

export interface GetAiChatConversationsVariables {
  workspaceId: UUIDString
  limit?: number | null
}

export interface GetAllUsersData {
  users: ({
    id: string
    email: string
    displayName?: string | null
    photoUrl?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & User_Key)[]
}

export interface GetAuditLogsBySeverityData {
  auditLogs: ({
    id: UUIDString
    workspaceId?: UUIDString | null
    userId?: string | null
    action: string
    resource?: string | null
    resourceId?: string | null
    ipAddress?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface GetAuditLogsBySeverityVariables {
  severity: string
  workspaceId?: UUIDString | null
  limit?: number | null
}

export interface GetAuditLogsByUserData {
  auditLogs: ({
    id: UUIDString
    workspaceId?: UUIDString | null
    userId?: string | null
    action: string
    resource?: string | null
    resourceId?: string | null
    ipAddress?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface GetAuditLogsByUserVariables {
  userId: string
  limit?: number | null
}

export interface GetAuditLogsByWorkspaceData {
  auditLogs: ({
    id: UUIDString
    workspaceId?: UUIDString | null
    userId?: string | null
    action: string
    resource?: string | null
    resourceId?: string | null
    ipAddress?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface GetAuditLogsByWorkspaceVariables {
  workspaceId: UUIDString
  limit?: number | null
}

export interface GetAuditLogsData {
  auditLogs: ({
    id: UUIDString
    workspaceId?: UUIDString | null
    userId?: string | null
    action: string
    resource?: string | null
    resourceId?: string | null
    ipAddress?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface GetAuditLogsVariables {
  workspaceId?: UUIDString | null
  limit?: number | null
  userId?: string | null
}

export interface GetBusinessProfileData {
  businessProfiles: ({
    id: UUIDString
    workspaceId: UUIDString
    name: string
    industry?: string | null
    description?: string | null
    location?: string | null
    website?: string | null
    employeeCount?: number | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & BusinessProfile_Key)[]
}

export interface GetBusinessProfileVariables {
  workspaceId: UUIDString
}

export interface GetConcurrentSessionsData {
  userSessions: ({
    id: string
    userId: string
    workspaceId?: UUIDString | null
    ipAddress?: string | null
    userAgent?: string | null
    deviceType?: string | null
    lastActivityAt: TimestampString
    expiresAt: TimestampString
    createdAt: TimestampString
  } & UserSession_Key)[]
}

export interface GetConcurrentSessionsVariables {
  userId: string
  limit?: number | null
}

export interface GetConversationData {
  conversation?: {
    id: UUIDString
    workspaceId: UUIDString
    title?: string | null
    summary?: string | null
    type: string
    metadata?: string | null
    lastMessageAt?: TimestampString | null
    messageCount?: number | null
    createdBy: string
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Conversation_Key
}

export interface GetConversationMessagesData {
  messages: ({
    id: UUIDString
    conversationId: UUIDString
    userId?: string | null
    role: string
    content: string
    status: string
    parentMessageId?: UUIDString | null
    metadata?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Message_Key)[]
}

export interface GetConversationMessagesVariables {
  conversationId: UUIDString
  limit?: number | null
  offset?: number | null
}

export interface GetConversationMessagesWithReactionsData {
  messages: ({
    id: UUIDString
    conversationId: UUIDString
    userId?: string | null
    role: string
    content: string
    status: string
    parentMessageId?: UUIDString | null
    metadata?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Message_Key)[]
}

export interface GetConversationMessagesWithReactionsVariables {
  conversationId: UUIDString
  limit?: number | null
  offset?: number | null
}

export interface GetConversationParticipantData {
  conversationParticipant?: {
    joinedAt: TimestampString
    lastReadAt?: TimestampString | null
    unreadCount?: number | null
    isActive: boolean
    isMuted: boolean
  }
}

export interface GetConversationParticipantVariables {
  conversationId: UUIDString
}

export interface GetConversationVariables {
  conversationId: UUIDString
}

export interface GetConversationWithParticipantsData {
  conversation?: {
    id: UUIDString
    workspaceId: UUIDString
    title?: string | null
    summary?: string | null
    type: string
    metadata?: string | null
    lastMessageAt?: TimestampString | null
    messageCount?: number | null
    createdBy: string
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Conversation_Key
  conversationParticipants: ({
    userId: string
    joinedAt: TimestampString
    lastReadAt?: TimestampString | null
    unreadCount?: number | null
    isActive: boolean
    isMuted: boolean
  })[]
}

export interface GetConversationWithParticipantsVariables {
  conversationId: UUIDString
}

export interface GetCurrentUserData {
  user?: {
    id: string
    email: string
    displayName?: string | null
    photoUrl?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & User_Key
}

export interface GetDataAccessLogsData {
  auditLogs: ({
    id: UUIDString
    workspaceId?: UUIDString | null
    userId?: string | null
    action: string
    resource?: string | null
    resourceId?: string | null
    ipAddress?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface GetDataAccessLogsVariables {
  workspaceId: UUIDString
  resourceType?: string | null
}

export interface GetDirectConversationData {
  conversations: ({
    id: UUIDString
    workspaceId: UUIDString
    title?: string | null
    type: string
    lastMessageAt?: TimestampString | null
    messageCount?: number | null
  } & Conversation_Key)[]
}

export interface GetDirectConversationVariables {
  workspaceId: UUIDString
}

export interface GetEmbeddingByEntityData {
  embeddings: ({
    id: UUIDString
    workspaceId: UUIDString
    entityType: string
    entityId: string
    contentHash: string
    content: string
    model: string
    tokenCount?: number | null
    metadata?: string | null
    version: number
    isActive: boolean
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Embedding_Key)[]
}

export interface GetEmbeddingByEntityVariables {
  workspaceId: UUIDString
  entityType: string
  entityId: string
}

export interface GetEmbeddingsData {
  embeddings: ({
    id: UUIDString
    workspaceId: UUIDString
    entityType: string
    entityId: string
    contentHash: string
    content: string
    model: string
    tokenCount?: number | null
    metadata?: string | null
    version: number
    isActive: boolean
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Embedding_Key)[]
}

export interface GetEmbeddingsVariables {
  workspaceId: UUIDString
  entityType?: string | null
  limit?: number | null
}

export interface GetFailedLoginAttemptsData {
  auditLogs: ({
    id: UUIDString
    userId?: string | null
    action: string
    ipAddress?: string | null
    userAgent?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface GetFailedLoginAttemptsVariables {
  ipAddress?: string | null
  userId?: string | null
}

export interface GetLlmKeyByProviderAndEntityData {
  llmKeys: ({
    id: UUIDString
    entityType: string
    entityId: string
    provider: string
    encryptedKey?: string | null
    encryptionIV?: string | null
    config?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & LlmKey_Key)[]
}

export interface GetLlmKeyByProviderAndEntityVariables {
  provider: string
  entityType: string
  entityId: string
}

export interface GetLlmKeysByEntityData {
  llmKeys: ({
    id: UUIDString
    entityType: string
    entityId: string
    provider: string
    config?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & LlmKey_Key)[]
}

export interface GetLlmKeysByEntityVariables {
  entityType: string
  entityId: string
}

export interface GetMessageReactionsData {
  messageReactions: ({
    userId: string
    reaction: string
    createdAt: TimestampString
  })[]
}

export interface GetMessageReactionsVariables {
  messageId: UUIDString
}

export interface GetPartnerPreferencesData {
  partnerPreferencesCollection: ({
    id: UUIDString
    workspaceId: UUIDString
    industries?: string[] | null
    locations?: string[] | null
    minEmployeeCount?: number | null
    maxEmployeeCount?: number | null
    skillsNeeded?: string[] | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & PartnerPreferences_Key)[]
}

export interface GetPartnerPreferencesVariables {
  workspaceId: UUIDString
}

export interface GetPendingInvitationsData {
  workspaceInvitations: ({
    id: UUIDString
    email: string
    role: string
    status: string
    createdAt: TimestampString
    expiresAt?: TimestampString | null
    workspaceId: UUIDString
    invitedBy: string
  } & WorkspaceInvitation_Key)[]
}

export interface GetPendingInvitationsVariables {
  email: string
}

export interface GetProfileLlmKeysData {
  llmKeys: ({
    id: UUIDString
    entityType: string
    entityId: string
    provider: string
    encryptedKey?: string | null
    encryptionIV?: string | null
    config?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & LlmKey_Key)[]
}

export interface GetProfileLlmKeysVariables {
  provider: string
  profileIds: string[]
}

export interface GetSessionData {
  userSession?: {
    id: string
    userId: string
    workspaceId?: UUIDString | null
    ipAddress?: string | null
    userAgent?: string | null
    deviceType?: string | null
    isActive: boolean
    lastActivityAt: TimestampString
    expiresAt: TimestampString
    revokedAt?: TimestampString | null
    revokedBy?: string | null
    revokedReason?: string | null
    metadata?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & UserSession_Key
}

export interface GetSessionVariables {
  sessionId: string
}

export interface GetSessionsByIpData {
  userSessions: ({
    id: string
    userId: string
    workspaceId?: UUIDString | null
    ipAddress?: string | null
    userAgent?: string | null
    deviceType?: string | null
    isActive: boolean
    lastActivityAt: TimestampString
    expiresAt: TimestampString
    revokedAt?: TimestampString | null
    revokedReason?: string | null
    createdAt: TimestampString
  } & UserSession_Key)[]
}

export interface GetSessionsByIpVariables {
  ipAddress: string
}

export interface GetSuspiciousActivityData {
  auditLogs: ({
    id: UUIDString
    workspaceId?: UUIDString | null
    userId?: string | null
    action: string
    resource?: string | null
    resourceId?: string | null
    ipAddress?: string | null
    userAgent?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface GetSuspiciousActivityVariables {
  workspaceId: UUIDString
}

export interface GetTotalUnreadCountData {
  conversationParticipants: ({
    unreadCount?: number | null
  })[]
}

export interface GetUnreadCountsData {
  conversationParticipants: ({
    conversationId: UUIDString
    unreadCount?: number | null
    lastReadAt?: TimestampString | null
  })[]
}

export interface GetUploadData {
  upload?: {
    id: UUIDString
    name: string
    src: string
    contentType?: string | null
    workspaceId: UUIDString
    uploadedBy?: string | null
  } & Upload_Key
}

export interface GetUploadVariables {
  id: UUIDString
}

export interface GetUserConversationsData {
  conversationParticipants: ({
    conversationId: UUIDString
    joinedAt: TimestampString
    lastReadAt?: TimestampString | null
    unreadCount?: number | null
    isMuted: boolean
  })[]
}

export interface GetUserConversationsVariables {
  limit?: number | null
  offset?: number | null
}

export interface GetUserData {
  user?: {
    id: string
    email: string
    displayName?: string | null
    photoUrl?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & User_Key
}

export interface GetUserLlmKeyData {
  llmKeys: ({
    id: UUIDString
    entityType: string
    entityId: string
    provider: string
    encryptedKey?: string | null
    encryptionIV?: string | null
    config?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & LlmKey_Key)[]
}

export interface GetUserLlmKeyVariables {
  provider: string
}

export interface GetUserMessageReactionData {
  messageReactions: ({
    reaction: string
    createdAt: TimestampString
  })[]
}

export interface GetUserMessageReactionVariables {
  messageId: UUIDString
}

export interface GetUserProfilesData {
  profiles: ({
    id: UUIDString
    userId: string
    name: string
    bio?: string | null
    avatarUrl?: string | null
    skills?: string[] | null
    interests?: string[] | null
    isDefault: boolean
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Profile_Key)[]
}

export interface GetUserSessionsData {
  userSessions: ({
    id: string
    userId: string
    workspaceId?: UUIDString | null
    ipAddress?: string | null
    userAgent?: string | null
    deviceType?: string | null
    isActive: boolean
    lastActivityAt: TimestampString
    expiresAt: TimestampString
    revokedAt?: TimestampString | null
    revokedBy?: string | null
    revokedReason?: string | null
    metadata?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & UserSession_Key)[]
}

export interface GetUserSessionsVariables {
  userId: string
  limit?: number | null
}

export interface GetUserVariables {
  id: string
}

export interface GetUserWorkspacesData {
  workspaceMembers: ({
    workspaceId: UUIDString
    role: string
    joinedAt: TimestampString
  })[]
}

export interface GetUserWorkspacesVariables {
  userId: string
}

export interface GetWorkspaceData {
  workspace?: {
    id: UUIDString
    name: string
    description?: string | null
    logoUrl?: string | null
    createdBy: string
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Workspace_Key
}

export interface GetWorkspaceInvitationsData {
  workspaceInvitations: ({
    id: UUIDString
    workspaceId: UUIDString
    email: string
    role: string
    invitedBy: string
    status: string
    createdAt: TimestampString
    expiresAt?: TimestampString | null
  } & WorkspaceInvitation_Key)[]
}

export interface GetWorkspaceInvitationsVariables {
  workspaceId: UUIDString
}

export interface GetWorkspaceLlmKeyData {
  llmKeys: ({
    id: UUIDString
    entityType: string
    entityId: string
    provider: string
    encryptedKey?: string | null
    encryptionIV?: string | null
    config?: string | null
    createdAt: TimestampString
    updatedAt: TimestampString
  } & LlmKey_Key)[]
}

export interface GetWorkspaceLlmKeyVariables {
  provider: string
  workspaceId: string
}

export interface GetWorkspaceMembersData {
  workspaceMembers: ({
    role: string
    joinedAt: TimestampString
    userId: string
    profileId?: UUIDString | null
  })[]
}

export interface GetWorkspaceMembersVariables {
  workspaceId: UUIDString
}

export interface GetWorkspaceSecurityEventsData {
  auditLogs: ({
    id: UUIDString
    workspaceId?: UUIDString | null
    userId?: string | null
    action: string
    resource?: string | null
    resourceId?: string | null
    ipAddress?: string | null
    userAgent?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface GetWorkspaceSecurityEventsVariables {
  workspaceId: UUIDString
}

export interface GetWorkspaceVariables {
  id: UUIDString
}

export interface IncrementUnreadCountsData {
  updateConversation?: Conversation_Key | null
}

export interface IncrementUnreadCountsVariables {
  conversationId: UUIDString
}

export interface Investment_Key {
  id: UUIDString
  __typename?: 'Investment_Key'
}

export interface InviteToWorkspaceData {
  inviteToWorkspace: WorkspaceInvitation_Key
}

export interface InviteToWorkspaceVariables {
  workspaceId: UUIDString
  email: string
  role: string
}

export interface Invoice_Key {
  id: UUIDString
  __typename?: 'Invoice_Key'
}

export interface Job_Key {
  id: UUIDString
  __typename?: 'Job_Key'
}

export interface LeaveConversationData {
  leaveConversation?: ConversationParticipant_Key | null
}

export interface LeaveConversationVariables {
  conversationId: UUIDString
}

export interface LinkUserPhotoUploadData {
  updateUser?: User_Key | null
}

export interface LinkUserPhotoUploadVariables {
  userId: string
  uploadId: UUIDString
}

export interface ListUploadsByWorkspaceData {
  uploads: ({
    id: UUIDString
    name: string
    src: string
    contentType?: string | null
    createdAt: TimestampString
  } & Upload_Key)[]
}

export interface ListUploadsByWorkspaceVariables {
  workspaceId: UUIDString
}

export interface LlmKey_Key {
  id: UUIDString
  __typename?: 'LlmKey_Key'
}

export interface Location_Key {
  id: UUIDString
  __typename?: 'Location_Key'
}

export interface LogAuthenticationEventData {
  logAuthEvent: AuditLog_Key
}

export interface LogAuthenticationEventVariables {
  userId?: string | null
  action: string
  ipAddress: string
  userAgent?: string | null
  status: string
  sessionId?: string | null
  metadata?: string | null
}

export interface LogBatchDataOperationData {
  logBatchOperation: AuditLog_Key
}

export interface LogBatchDataOperationVariables {
  workspaceId: UUIDString
  userId: string
  action: string
  resource: string
  ipAddress?: string | null
  sessionId?: string | null
  metadata?: string | null
}

export interface LogDataAccessData {
  logDataAccess: AuditLog_Key
}

export interface LogDataAccessVariables {
  workspaceId: UUIDString
  userId: string
  action: string
  resource: string
  resourceId: string
  ipAddress?: string | null
  sessionId?: string | null
  metadata?: string | null
}

export interface LogFailedAuthenticationData {
  logFailedAuth: AuditLog_Key
}

export interface LogFailedAuthenticationVariables {
  userId?: string | null
  ipAddress: string
  userAgent?: string | null
  action: string
  metadata?: string | null
}

export interface LogPermissionChangeData {
  logPermissionChange: AuditLog_Key
}

export interface LogPermissionChangeVariables {
  workspaceId: UUIDString
  userId: string
  action: string
  resourceId: string
  ipAddress?: string | null
  sessionId?: string | null
  metadata: string
}

export interface LogSecurityEventData {
  logSecurityEvent: AuditLog_Key
}

export interface LogSecurityEventVariables {
  workspaceId?: UUIDString | null
  userId?: string | null
  action: string
  resource?: string | null
  resourceId?: string | null
  ipAddress?: string | null
  userAgent?: string | null
  severity: string
  status: string
  metadata?: string | null
  sessionId?: string | null
}

export interface LogWorkspaceEventData {
  logWorkspaceEvent: AuditLog_Key
}

export interface LogWorkspaceEventVariables {
  workspaceId: UUIDString
  userId: string
  action: string
  resourceId?: string | null
  ipAddress?: string | null
  sessionId?: string | null
  severity: string
  metadata?: string | null
}

export interface MarkMessagesAsReadData {
  updateParticipantRead?: ConversationParticipant_Key | null
}

export interface MarkMessagesAsReadVariables {
  conversationId: UUIDString
}

export interface MatchBusinessToProfilesData {
  businessProfile?: {
    id: UUIDString
    name: string
    description?: string | null
  } & BusinessProfile_Key
}

export interface MatchBusinessToProfilesVariables {
  businessProfileId: UUIDString
}

export interface MatchProfileToBusinessesData {
  profile?: {
    id: UUIDString
    name: string
    bio?: string | null
  } & Profile_Key
}

export interface MatchProfileToBusinessesVariables {
  profileId: UUIDString
}

export interface MessageReaction_Key {
  messageId: UUIDString
  userId: string
  reaction: string
  __typename?: 'MessageReaction_Key'
}

export interface Message_Key {
  id: UUIDString
  __typename?: 'Message_Key'
}

export interface Offer_Key {
  id: UUIDString
  __typename?: 'Offer_Key'
}

export interface PartnerPreferences_Key {
  id: UUIDString
  __typename?: 'PartnerPreferences_Key'
}

export interface Post_Key {
  id: UUIDString
  __typename?: 'Post_Key'
}

export interface Product_Key {
  id: UUIDString
  __typename?: 'Product_Key'
}

export interface ProfileAddress_Key {
  profileId: UUIDString
  addressId: UUIDString
  __typename?: 'ProfileAddress_Key'
}

export interface Profile_Key {
  id: UUIDString
  __typename?: 'Profile_Key'
}

export interface ProfilesBySimilarityData {
  profiles_bioEmbedding_similarity: ({
    id: UUIDString
    userId: string
    name: string
    bio?: string | null
    skills?: string[] | null
    interests?: string[] | null
    avatarUrl?: string | null
  } & Profile_Key)[]
}

export interface ProfilesBySimilarityVariables {
  descriptionText: string
  limit?: number | null
}

export interface ProjectDocument_Key {
  projectId: UUIDString
  documentId: UUIDString
  __typename?: 'ProjectDocument_Key'
}

export interface ProjectTaskChecklist_Key {
  id: UUIDString
  __typename?: 'ProjectTaskChecklist_Key'
}

export interface ProjectTaskComment_Key {
  id: UUIDString
  __typename?: 'ProjectTaskComment_Key'
}

export interface ProjectTask_Key {
  id: UUIDString
  __typename?: 'ProjectTask_Key'
}

export interface ProjectTeamMember_Key {
  id: UUIDString
  __typename?: 'ProjectTeamMember_Key'
}

export interface Project_Key {
  id: UUIDString
  __typename?: 'Project_Key'
}

export interface Quote_Key {
  id: UUIDString
  __typename?: 'Quote_Key'
}

export interface Recipe_Key {
  id: UUIDString
  __typename?: 'Recipe_Key'
}

export interface RemoveConversationParticipantData {
  removeParticipant?: ConversationParticipant_Key | null
}

export interface RemoveConversationParticipantVariables {
  conversationId: UUIDString
  userId: string
}

export interface RemoveReactionData {
  removeReaction?: MessageReaction_Key | null
}

export interface RemoveReactionVariables {
  messageId: UUIDString
  reaction: string
}

export interface RemoveWorkspaceMemberData {
  removeWorkspaceMember?: WorkspaceMember_Key | null
}

export interface RemoveWorkspaceMemberVariables {
  workspaceId: UUIDString
  userId: string
}

export interface Rental_Key {
  id: UUIDString
  __typename?: 'Rental_Key'
}

export interface RevokeSessionWithAuditData {
  revokeSession?: UserSession_Key | null
  logSessionRevocation: AuditLog_Key
}

export interface RevokeSessionWithAuditVariables {
  id: string
  revokedBy: string
  revokedReason: string
  ipAddress?: string | null
}

export interface RevokeUserSessionData {
  revokeUserSession?: UserSession_Key | null
}

export interface RevokeUserSessionVariables {
  id: string
  revokedBy: string
  revokedReason?: string | null
}

export interface SearchAuditLogsData {
  auditLogs: ({
    id: UUIDString
    workspaceId?: UUIDString | null
    userId?: string | null
    action: string
    resource?: string | null
    resourceId?: string | null
    ipAddress?: string | null
    userAgent?: string | null
    severity: string
    status: string
    metadata?: string | null
    sessionId?: string | null
    createdAt: TimestampString
  } & AuditLog_Key)[]
}

export interface SearchAuditLogsVariables {
  workspaceId?: UUIDString | null
  userId?: string | null
  action?: string | null
  resource?: string | null
  severity?: string | null
  status?: string | null
  startDate?: TimestampString | null
  endDate?: TimestampString | null
  limit?: number | null
}

export interface SearchBusinessProfilesByDescriptionData {
  businessProfiles_descriptionEmbedding_similarity: ({
    id: UUIDString
    workspaceId: UUIDString
    name: string
    industry?: string | null
    description?: string | null
    location?: string | null
    website?: string | null
    employeeCount?: number | null
  } & BusinessProfile_Key)[]
}

export interface SearchBusinessProfilesByDescriptionVariables {
  searchText: string
  limit?: number | null
}

export interface SearchConversationsData {
  conversations_summaryEmbedding_similarity: ({
    id: UUIDString
    workspaceId: UUIDString
    title?: string | null
    summary?: string | null
    type: string
    lastMessageAt?: TimestampString | null
    messageCount?: number | null
    createdBy: string
    createdAt: TimestampString
  } & Conversation_Key)[]
}

export interface SearchConversationsVariables {
  searchText: string
  workspaceId: UUIDString
  limit?: number | null
}

export interface SearchEmbeddingsData {
  embeddings_embedding_similarity: ({
    id: UUIDString
    workspaceId: UUIDString
    entityType: string
    entityId: string
    contentHash: string
    content: string
    model: string
    tokenCount?: number | null
    metadata?: string | null
    version: number
    createdAt: TimestampString
    updatedAt: TimestampString
  } & Embedding_Key)[]
}

export interface SearchEmbeddingsVariables {
  workspaceId: UUIDString
  searchText: string
  entityType?: string | null
  limit?: number | null
}

export interface SearchMessagesData {
  messages_contentEmbedding_similarity: ({
    id: UUIDString
    conversationId: UUIDString
    userId?: string | null
    role: string
    content: string
    status: string
    createdAt: TimestampString
  } & Message_Key)[]
}

export interface SearchMessagesVariables {
  searchText: string
  conversationId?: UUIDString | null
  limit?: number | null
}

export interface SearchPartnerPreferencesData {
  partnerPreferencesCollection_combinedEmbedding_similarity: ({
    id: UUIDString
    workspaceId: UUIDString
    industries?: string[] | null
    locations?: string[] | null
    minEmployeeCount?: number | null
    maxEmployeeCount?: number | null
    skillsNeeded?: string[] | null
  } & PartnerPreferences_Key)[]
}

export interface SearchPartnerPreferencesVariables {
  searchText: string
  limit?: number | null
}

export interface SearchProfilesByBioData {
  profiles_bioEmbedding_similarity: ({
    id: UUIDString
    userId: string
    name: string
    bio?: string | null
    skills?: string[] | null
    interests?: string[] | null
    avatarUrl?: string | null
  } & Profile_Key)[]
}

export interface SearchProfilesByBioVariables {
  searchText: string
  limit?: number | null
}

export interface SearchWorkspaceMessagesData {
  messages_contentEmbedding_similarity: ({
    id: UUIDString
    conversationId: UUIDString
    userId?: string | null
    role: string
    content: string
    status: string
    createdAt: TimestampString
  } & Message_Key)[]
}

export interface SearchWorkspaceMessagesVariables {
  searchText: string
  limit?: number | null
}

export interface SoftDeleteEmbeddingData {
  softDeleteEmbedding?: Embedding_Key | null
}

export interface SoftDeleteEmbeddingVariables {
  id: UUIDString
}

export interface TeamMemberAgency_Key {
  uid: UUIDString
  __typename?: 'TeamMemberAgency_Key'
}

export interface Transaction_Key {
  id: UUIDString
  __typename?: 'Transaction_Key'
}

export interface UpdateBusinessProfileData {
  updateBusinessProfile?: BusinessProfile_Key | null
}

export interface UpdateBusinessProfileVariables {
  id: UUIDString
  name?: string | null
  industry?: string | null
  description?: string | null
  location?: string | null
  website?: string | null
  employeeCount?: number | null
}

export interface UpdateBusinessProfileWithDescriptionData {
  updateBusinessProfile?: BusinessProfile_Key | null
}

export interface UpdateBusinessProfileWithDescriptionVariables {
  id: UUIDString
  name?: string | null
  industry?: string | null
  description: string
  location?: string | null
  website?: string | null
  employeeCount?: number | null
}

export interface UpdateConversationAfterMessageData {
  updateConversation?: Conversation_Key | null
}

export interface UpdateConversationAfterMessageVariables {
  conversationId: UUIDString
}

export interface UpdateConversationData {
  updateConversation?: Conversation_Key | null
}

export interface UpdateConversationParticipantData {
  updateParticipant?: ConversationParticipant_Key | null
}

export interface UpdateConversationParticipantVariables {
  conversationId: UUIDString
  isActive?: boolean | null
  isMuted?: boolean | null
}

export interface UpdateConversationVariables {
  conversationId: UUIDString
  title?: string | null
  summary?: string | null
  metadata?: string | null
}

export interface UpdateConversationWithSummaryData {
  updateConversation?: Conversation_Key | null
}

export interface UpdateConversationWithSummaryVariables {
  conversationId: UUIDString
  title?: string | null
  summary: string
  metadata?: string | null
}

export interface UpdateEmbeddingData {
  updateEmbedding?: Embedding_Key | null
}

export interface UpdateEmbeddingVariables {
  id: UUIDString
  content: string
  contentHash: string
  model: string
  tokenCount?: number | null
  metadata?: string | null
  version: number
}

export interface UpdateLlmKeyData {
  updateLLMKey?: LlmKey_Key | null
}

export interface UpdateLlmKeyVariables {
  id: UUIDString
  encryptedKey?: string | null
  encryptionIV?: string | null
  config?: string | null
}

export interface UpdatePartnerPreferencesData {
  updatePartnerPreferences?: PartnerPreferences_Key | null
}

export interface UpdatePartnerPreferencesVariables {
  id: UUIDString
  industries?: string[] | null
  locations?: string[] | null
  minEmployeeCount?: number | null
  maxEmployeeCount?: number | null
  skillsNeeded?: string[] | null
}

export interface UpdatePartnerPreferencesWithEmbeddingData {
  updatePartnerPreferences?: PartnerPreferences_Key | null
}

export interface UpdatePartnerPreferencesWithEmbeddingVariables {
  id: UUIDString
  industries?: string[] | null
  locations?: string[] | null
  minEmployeeCount?: number | null
  maxEmployeeCount?: number | null
  skillsNeeded?: string[] | null
  combinedText: string
}

export interface UpdateProfileData {
  updateProfile?: Profile_Key | null
}

export interface UpdateProfileVariables {
  id: UUIDString
  name?: string | null
  bio?: string | null
  avatarUrl?: string | null
  skills?: string[] | null
  interests?: string[] | null
  isDefault?: boolean | null
}

export interface UpdateProfileWithBioData {
  updateProfile?: Profile_Key | null
}

export interface UpdateProfileWithBioVariables {
  id: UUIDString
  name?: string | null
  bio: string
  avatarUrl?: string | null
  skills?: string[] | null
  interests?: string[] | null
  isDefault?: boolean | null
}

export interface UpdateUserData {
  updateUser?: User_Key | null
}

export interface UpdateUserSessionActivityData {
  updateUserSession?: UserSession_Key | null
}

export interface UpdateUserSessionActivityVariables {
  id: string
  workspaceId?: UUIDString | null
}

export interface UpdateUserVariables {
  displayName?: string | null
  photoUrl?: string | null
}

export interface UpdateWorkspaceData {
  updateWorkspace?: Workspace_Key | null
}

export interface UpdateWorkspaceMemberData {
  updateWorkspaceMember?: WorkspaceMember_Key | null
}

export interface UpdateWorkspaceMemberVariables {
  workspaceId: UUIDString
  userId: string
  profileId?: UUIDString | null
  role?: string | null
}

export interface UpdateWorkspaceVariables {
  id: UUIDString
  name?: string | null
  description?: string | null
  logoUrl?: string | null
}

export interface Upload_Key {
  id: UUIDString
  __typename?: 'Upload_Key'
}

export interface UserAddress_Key {
  userId: string
  addressId: UUIDString
  __typename?: 'UserAddress_Key'
}

export interface UserSession_Key {
  id: string
  __typename?: 'UserSession_Key'
}

export interface User_Key {
  id: string
  __typename?: 'User_Key'
}

export interface WorkspaceAddress_Key {
  workspaceId: UUIDString
  addressId: UUIDString
  __typename?: 'WorkspaceAddress_Key'
}

export interface WorkspaceInvitation_Key {
  id: UUIDString
  __typename?: 'WorkspaceInvitation_Key'
}

export interface WorkspaceMember_Key {
  id: UUIDString
  __typename?: 'WorkspaceMember_Key'
}

export interface Workspace_Key {
  id: UUIDString
  __typename?: 'Workspace_Key'
}

interface CreateConversationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateConversationVariables): MutationRef<CreateConversationData, CreateConversationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateConversationVariables): MutationRef<CreateConversationData, CreateConversationVariables>
  operationName: string
}
export const createConversationRef: CreateConversationRef

export function createConversation(vars: CreateConversationVariables): MutationPromise<CreateConversationData, CreateConversationVariables>
export function createConversation(dc: DataConnect, vars: CreateConversationVariables): MutationPromise<CreateConversationData, CreateConversationVariables>

interface CreateConversationWithSummaryRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateConversationWithSummaryVariables): MutationRef<CreateConversationWithSummaryData, CreateConversationWithSummaryVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateConversationWithSummaryVariables): MutationRef<CreateConversationWithSummaryData, CreateConversationWithSummaryVariables>
  operationName: string
}
export const createConversationWithSummaryRef: CreateConversationWithSummaryRef

export function createConversationWithSummary(vars: CreateConversationWithSummaryVariables): MutationPromise<CreateConversationWithSummaryData, CreateConversationWithSummaryVariables>
export function createConversationWithSummary(dc: DataConnect, vars: CreateConversationWithSummaryVariables): MutationPromise<CreateConversationWithSummaryData, CreateConversationWithSummaryVariables>

interface AddConversationParticipantRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: AddConversationParticipantVariables): MutationRef<AddConversationParticipantData, AddConversationParticipantVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: AddConversationParticipantVariables): MutationRef<AddConversationParticipantData, AddConversationParticipantVariables>
  operationName: string
}
export const addConversationParticipantRef: AddConversationParticipantRef

export function addConversationParticipant(vars: AddConversationParticipantVariables): MutationPromise<AddConversationParticipantData, AddConversationParticipantVariables>
export function addConversationParticipant(dc: DataConnect, vars: AddConversationParticipantVariables): MutationPromise<AddConversationParticipantData, AddConversationParticipantVariables>

interface CreateMessageRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateMessageVariables): MutationRef<CreateMessageData, CreateMessageVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateMessageVariables): MutationRef<CreateMessageData, CreateMessageVariables>
  operationName: string
}
export const createMessageRef: CreateMessageRef

export function createMessage(vars: CreateMessageVariables): MutationPromise<CreateMessageData, CreateMessageVariables>
export function createMessage(dc: DataConnect, vars: CreateMessageVariables): MutationPromise<CreateMessageData, CreateMessageVariables>

interface CreateMessageWithEmbeddingRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateMessageWithEmbeddingVariables): MutationRef<CreateMessageWithEmbeddingData, CreateMessageWithEmbeddingVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateMessageWithEmbeddingVariables): MutationRef<CreateMessageWithEmbeddingData, CreateMessageWithEmbeddingVariables>
  operationName: string
}
export const createMessageWithEmbeddingRef: CreateMessageWithEmbeddingRef

export function createMessageWithEmbedding(vars: CreateMessageWithEmbeddingVariables): MutationPromise<CreateMessageWithEmbeddingData, CreateMessageWithEmbeddingVariables>
export function createMessageWithEmbedding(dc: DataConnect, vars: CreateMessageWithEmbeddingVariables): MutationPromise<CreateMessageWithEmbeddingData, CreateMessageWithEmbeddingVariables>

interface UpdateConversationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateConversationVariables): MutationRef<UpdateConversationData, UpdateConversationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateConversationVariables): MutationRef<UpdateConversationData, UpdateConversationVariables>
  operationName: string
}
export const updateConversationRef: UpdateConversationRef

export function updateConversation(vars: UpdateConversationVariables): MutationPromise<UpdateConversationData, UpdateConversationVariables>
export function updateConversation(dc: DataConnect, vars: UpdateConversationVariables): MutationPromise<UpdateConversationData, UpdateConversationVariables>

interface UpdateConversationWithSummaryRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateConversationWithSummaryVariables): MutationRef<UpdateConversationWithSummaryData, UpdateConversationWithSummaryVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateConversationWithSummaryVariables): MutationRef<UpdateConversationWithSummaryData, UpdateConversationWithSummaryVariables>
  operationName: string
}
export const updateConversationWithSummaryRef: UpdateConversationWithSummaryRef

export function updateConversationWithSummary(vars: UpdateConversationWithSummaryVariables): MutationPromise<UpdateConversationWithSummaryData, UpdateConversationWithSummaryVariables>
export function updateConversationWithSummary(dc: DataConnect, vars: UpdateConversationWithSummaryVariables): MutationPromise<UpdateConversationWithSummaryData, UpdateConversationWithSummaryVariables>

interface MarkMessagesAsReadRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: MarkMessagesAsReadVariables): MutationRef<MarkMessagesAsReadData, MarkMessagesAsReadVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: MarkMessagesAsReadVariables): MutationRef<MarkMessagesAsReadData, MarkMessagesAsReadVariables>
  operationName: string
}
export const markMessagesAsReadRef: MarkMessagesAsReadRef

export function markMessagesAsRead(vars: MarkMessagesAsReadVariables): MutationPromise<MarkMessagesAsReadData, MarkMessagesAsReadVariables>
export function markMessagesAsRead(dc: DataConnect, vars: MarkMessagesAsReadVariables): MutationPromise<MarkMessagesAsReadData, MarkMessagesAsReadVariables>

interface AddReactionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: AddReactionVariables): MutationRef<AddReactionData, AddReactionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: AddReactionVariables): MutationRef<AddReactionData, AddReactionVariables>
  operationName: string
}
export const addReactionRef: AddReactionRef

export function addReaction(vars: AddReactionVariables): MutationPromise<AddReactionData, AddReactionVariables>
export function addReaction(dc: DataConnect, vars: AddReactionVariables): MutationPromise<AddReactionData, AddReactionVariables>

interface RemoveReactionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: RemoveReactionVariables): MutationRef<RemoveReactionData, RemoveReactionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: RemoveReactionVariables): MutationRef<RemoveReactionData, RemoveReactionVariables>
  operationName: string
}
export const removeReactionRef: RemoveReactionRef

export function removeReaction(vars: RemoveReactionVariables): MutationPromise<RemoveReactionData, RemoveReactionVariables>
export function removeReaction(dc: DataConnect, vars: RemoveReactionVariables): MutationPromise<RemoveReactionData, RemoveReactionVariables>

interface UpdateConversationParticipantRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateConversationParticipantVariables): MutationRef<UpdateConversationParticipantData, UpdateConversationParticipantVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateConversationParticipantVariables): MutationRef<UpdateConversationParticipantData, UpdateConversationParticipantVariables>
  operationName: string
}
export const updateConversationParticipantRef: UpdateConversationParticipantRef

export function updateConversationParticipant(vars: UpdateConversationParticipantVariables): MutationPromise<UpdateConversationParticipantData, UpdateConversationParticipantVariables>
export function updateConversationParticipant(dc: DataConnect, vars: UpdateConversationParticipantVariables): MutationPromise<UpdateConversationParticipantData, UpdateConversationParticipantVariables>

interface DeleteMessageRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: DeleteMessageVariables): MutationRef<DeleteMessageData, DeleteMessageVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: DeleteMessageVariables): MutationRef<DeleteMessageData, DeleteMessageVariables>
  operationName: string
}
export const deleteMessageRef: DeleteMessageRef

export function deleteMessage(vars: DeleteMessageVariables): MutationPromise<DeleteMessageData, DeleteMessageVariables>
export function deleteMessage(dc: DataConnect, vars: DeleteMessageVariables): MutationPromise<DeleteMessageData, DeleteMessageVariables>

interface EditMessageRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: EditMessageVariables): MutationRef<EditMessageData, EditMessageVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: EditMessageVariables): MutationRef<EditMessageData, EditMessageVariables>
  operationName: string
}
export const editMessageRef: EditMessageRef

export function editMessage(vars: EditMessageVariables): MutationPromise<EditMessageData, EditMessageVariables>
export function editMessage(dc: DataConnect, vars: EditMessageVariables): MutationPromise<EditMessageData, EditMessageVariables>

interface EditMessageWithEmbeddingRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: EditMessageWithEmbeddingVariables): MutationRef<EditMessageWithEmbeddingData, EditMessageWithEmbeddingVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: EditMessageWithEmbeddingVariables): MutationRef<EditMessageWithEmbeddingData, EditMessageWithEmbeddingVariables>
  operationName: string
}
export const editMessageWithEmbeddingRef: EditMessageWithEmbeddingRef

export function editMessageWithEmbedding(vars: EditMessageWithEmbeddingVariables): MutationPromise<EditMessageWithEmbeddingData, EditMessageWithEmbeddingVariables>
export function editMessageWithEmbedding(dc: DataConnect, vars: EditMessageWithEmbeddingVariables): MutationPromise<EditMessageWithEmbeddingData, EditMessageWithEmbeddingVariables>

interface DeleteConversationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: DeleteConversationVariables): MutationRef<DeleteConversationData, DeleteConversationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: DeleteConversationVariables): MutationRef<DeleteConversationData, DeleteConversationVariables>
  operationName: string
}
export const deleteConversationRef: DeleteConversationRef

export function deleteConversation(vars: DeleteConversationVariables): MutationPromise<DeleteConversationData, DeleteConversationVariables>
export function deleteConversation(dc: DataConnect, vars: DeleteConversationVariables): MutationPromise<DeleteConversationData, DeleteConversationVariables>

interface LeaveConversationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LeaveConversationVariables): MutationRef<LeaveConversationData, LeaveConversationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LeaveConversationVariables): MutationRef<LeaveConversationData, LeaveConversationVariables>
  operationName: string
}
export const leaveConversationRef: LeaveConversationRef

export function leaveConversation(vars: LeaveConversationVariables): MutationPromise<LeaveConversationData, LeaveConversationVariables>
export function leaveConversation(dc: DataConnect, vars: LeaveConversationVariables): MutationPromise<LeaveConversationData, LeaveConversationVariables>

interface RemoveConversationParticipantRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: RemoveConversationParticipantVariables): MutationRef<RemoveConversationParticipantData, RemoveConversationParticipantVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: RemoveConversationParticipantVariables): MutationRef<RemoveConversationParticipantData, RemoveConversationParticipantVariables>
  operationName: string
}
export const removeConversationParticipantRef: RemoveConversationParticipantRef

export function removeConversationParticipant(vars: RemoveConversationParticipantVariables): MutationPromise<RemoveConversationParticipantData, RemoveConversationParticipantVariables>
export function removeConversationParticipant(dc: DataConnect, vars: RemoveConversationParticipantVariables): MutationPromise<RemoveConversationParticipantData, RemoveConversationParticipantVariables>

interface UpdateConversationAfterMessageRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateConversationAfterMessageVariables): MutationRef<UpdateConversationAfterMessageData, UpdateConversationAfterMessageVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateConversationAfterMessageVariables): MutationRef<UpdateConversationAfterMessageData, UpdateConversationAfterMessageVariables>
  operationName: string
}
export const updateConversationAfterMessageRef: UpdateConversationAfterMessageRef

export function updateConversationAfterMessage(vars: UpdateConversationAfterMessageVariables): MutationPromise<UpdateConversationAfterMessageData, UpdateConversationAfterMessageVariables>
export function updateConversationAfterMessage(dc: DataConnect, vars: UpdateConversationAfterMessageVariables): MutationPromise<UpdateConversationAfterMessageData, UpdateConversationAfterMessageVariables>

interface IncrementUnreadCountsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: IncrementUnreadCountsVariables): MutationRef<IncrementUnreadCountsData, IncrementUnreadCountsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: IncrementUnreadCountsVariables): MutationRef<IncrementUnreadCountsData, IncrementUnreadCountsVariables>
  operationName: string
}
export const incrementUnreadCountsRef: IncrementUnreadCountsRef

export function incrementUnreadCounts(vars: IncrementUnreadCountsVariables): MutationPromise<IncrementUnreadCountsData, IncrementUnreadCountsVariables>
export function incrementUnreadCounts(dc: DataConnect, vars: IncrementUnreadCountsVariables): MutationPromise<IncrementUnreadCountsData, IncrementUnreadCountsVariables>

interface CreateDirectConversationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateDirectConversationVariables): MutationRef<CreateDirectConversationData, CreateDirectConversationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateDirectConversationVariables): MutationRef<CreateDirectConversationData, CreateDirectConversationVariables>
  operationName: string
}
export const createDirectConversationRef: CreateDirectConversationRef

export function createDirectConversation(vars: CreateDirectConversationVariables): MutationPromise<CreateDirectConversationData, CreateDirectConversationVariables>
export function createDirectConversation(dc: DataConnect, vars: CreateDirectConversationVariables): MutationPromise<CreateDirectConversationData, CreateDirectConversationVariables>

interface AddMultipleParticipantsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: AddMultipleParticipantsVariables): MutationRef<AddMultipleParticipantsData, AddMultipleParticipantsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: AddMultipleParticipantsVariables): MutationRef<AddMultipleParticipantsData, AddMultipleParticipantsVariables>
  operationName: string
}
export const addMultipleParticipantsRef: AddMultipleParticipantsRef

export function addMultipleParticipants(vars: AddMultipleParticipantsVariables): MutationPromise<AddMultipleParticipantsData, AddMultipleParticipantsVariables>
export function addMultipleParticipants(dc: DataConnect, vars: AddMultipleParticipantsVariables): MutationPromise<AddMultipleParticipantsData, AddMultipleParticipantsVariables>

interface GetConversationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetConversationVariables): QueryRef<GetConversationData, GetConversationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetConversationVariables): QueryRef<GetConversationData, GetConversationVariables>
  operationName: string
}
export const getConversationRef: GetConversationRef

export function getConversation(vars: GetConversationVariables): QueryPromise<GetConversationData, GetConversationVariables>
export function getConversation(dc: DataConnect, vars: GetConversationVariables): QueryPromise<GetConversationData, GetConversationVariables>

interface GetConversationWithParticipantsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetConversationWithParticipantsVariables): QueryRef<GetConversationWithParticipantsData, GetConversationWithParticipantsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetConversationWithParticipantsVariables): QueryRef<GetConversationWithParticipantsData, GetConversationWithParticipantsVariables>
  operationName: string
}
export const getConversationWithParticipantsRef: GetConversationWithParticipantsRef

export function getConversationWithParticipants(vars: GetConversationWithParticipantsVariables): QueryPromise<GetConversationWithParticipantsData, GetConversationWithParticipantsVariables>
export function getConversationWithParticipants(dc: DataConnect, vars: GetConversationWithParticipantsVariables): QueryPromise<GetConversationWithParticipantsData, GetConversationWithParticipantsVariables>

interface GetConversationMessagesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetConversationMessagesVariables): QueryRef<GetConversationMessagesData, GetConversationMessagesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetConversationMessagesVariables): QueryRef<GetConversationMessagesData, GetConversationMessagesVariables>
  operationName: string
}
export const getConversationMessagesRef: GetConversationMessagesRef

export function getConversationMessages(vars: GetConversationMessagesVariables): QueryPromise<GetConversationMessagesData, GetConversationMessagesVariables>
export function getConversationMessages(dc: DataConnect, vars: GetConversationMessagesVariables): QueryPromise<GetConversationMessagesData, GetConversationMessagesVariables>

interface GetConversationMessagesWithReactionsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetConversationMessagesWithReactionsVariables): QueryRef<GetConversationMessagesWithReactionsData, GetConversationMessagesWithReactionsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetConversationMessagesWithReactionsVariables): QueryRef<GetConversationMessagesWithReactionsData, GetConversationMessagesWithReactionsVariables>
  operationName: string
}
export const getConversationMessagesWithReactionsRef: GetConversationMessagesWithReactionsRef

export function getConversationMessagesWithReactions(vars: GetConversationMessagesWithReactionsVariables): QueryPromise<GetConversationMessagesWithReactionsData, GetConversationMessagesWithReactionsVariables>
export function getConversationMessagesWithReactions(dc: DataConnect, vars: GetConversationMessagesWithReactionsVariables): QueryPromise<GetConversationMessagesWithReactionsData, GetConversationMessagesWithReactionsVariables>

interface GetUserConversationsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars?: GetUserConversationsVariables): QueryRef<GetUserConversationsData, GetUserConversationsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars?: GetUserConversationsVariables): QueryRef<GetUserConversationsData, GetUserConversationsVariables>
  operationName: string
}
export const getUserConversationsRef: GetUserConversationsRef

export function getUserConversations(vars?: GetUserConversationsVariables): QueryPromise<GetUserConversationsData, GetUserConversationsVariables>
export function getUserConversations(dc: DataConnect, vars?: GetUserConversationsVariables): QueryPromise<GetUserConversationsData, GetUserConversationsVariables>

interface GetDirectConversationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetDirectConversationVariables): QueryRef<GetDirectConversationData, GetDirectConversationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetDirectConversationVariables): QueryRef<GetDirectConversationData, GetDirectConversationVariables>
  operationName: string
}
export const getDirectConversationRef: GetDirectConversationRef

export function getDirectConversation(vars: GetDirectConversationVariables): QueryPromise<GetDirectConversationData, GetDirectConversationVariables>
export function getDirectConversation(dc: DataConnect, vars: GetDirectConversationVariables): QueryPromise<GetDirectConversationData, GetDirectConversationVariables>

interface SearchConversationsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: SearchConversationsVariables): QueryRef<SearchConversationsData, SearchConversationsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: SearchConversationsVariables): QueryRef<SearchConversationsData, SearchConversationsVariables>
  operationName: string
}
export const searchConversationsRef: SearchConversationsRef

export function searchConversations(vars: SearchConversationsVariables): QueryPromise<SearchConversationsData, SearchConversationsVariables>
export function searchConversations(dc: DataConnect, vars: SearchConversationsVariables): QueryPromise<SearchConversationsData, SearchConversationsVariables>

interface SearchMessagesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: SearchMessagesVariables): QueryRef<SearchMessagesData, SearchMessagesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: SearchMessagesVariables): QueryRef<SearchMessagesData, SearchMessagesVariables>
  operationName: string
}
export const searchMessagesRef: SearchMessagesRef

export function searchMessages(vars: SearchMessagesVariables): QueryPromise<SearchMessagesData, SearchMessagesVariables>
export function searchMessages(dc: DataConnect, vars: SearchMessagesVariables): QueryPromise<SearchMessagesData, SearchMessagesVariables>

interface SearchWorkspaceMessagesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: SearchWorkspaceMessagesVariables): QueryRef<SearchWorkspaceMessagesData, SearchWorkspaceMessagesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: SearchWorkspaceMessagesVariables): QueryRef<SearchWorkspaceMessagesData, SearchWorkspaceMessagesVariables>
  operationName: string
}
export const searchWorkspaceMessagesRef: SearchWorkspaceMessagesRef

export function searchWorkspaceMessages(vars: SearchWorkspaceMessagesVariables): QueryPromise<SearchWorkspaceMessagesData, SearchWorkspaceMessagesVariables>
export function searchWorkspaceMessages(dc: DataConnect, vars: SearchWorkspaceMessagesVariables): QueryPromise<SearchWorkspaceMessagesData, SearchWorkspaceMessagesVariables>

interface GetUnreadCountsRef {
  /* Allow users to create refs without passing in DataConnect */
  (): QueryRef<GetUnreadCountsData, undefined>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect): QueryRef<GetUnreadCountsData, undefined>
  operationName: string
}
export const getUnreadCountsRef: GetUnreadCountsRef

export function getUnreadCounts(): QueryPromise<GetUnreadCountsData, undefined>
export function getUnreadCounts(dc: DataConnect): QueryPromise<GetUnreadCountsData, undefined>

interface GetTotalUnreadCountRef {
  /* Allow users to create refs without passing in DataConnect */
  (): QueryRef<GetTotalUnreadCountData, undefined>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect): QueryRef<GetTotalUnreadCountData, undefined>
  operationName: string
}
export const getTotalUnreadCountRef: GetTotalUnreadCountRef

export function getTotalUnreadCount(): QueryPromise<GetTotalUnreadCountData, undefined>
export function getTotalUnreadCount(dc: DataConnect): QueryPromise<GetTotalUnreadCountData, undefined>

interface GetConversationParticipantRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetConversationParticipantVariables): QueryRef<GetConversationParticipantData, GetConversationParticipantVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetConversationParticipantVariables): QueryRef<GetConversationParticipantData, GetConversationParticipantVariables>
  operationName: string
}
export const getConversationParticipantRef: GetConversationParticipantRef

export function getConversationParticipant(vars: GetConversationParticipantVariables): QueryPromise<GetConversationParticipantData, GetConversationParticipantVariables>
export function getConversationParticipant(dc: DataConnect, vars: GetConversationParticipantVariables): QueryPromise<GetConversationParticipantData, GetConversationParticipantVariables>

interface GetMessageReactionsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetMessageReactionsVariables): QueryRef<GetMessageReactionsData, GetMessageReactionsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetMessageReactionsVariables): QueryRef<GetMessageReactionsData, GetMessageReactionsVariables>
  operationName: string
}
export const getMessageReactionsRef: GetMessageReactionsRef

export function getMessageReactions(vars: GetMessageReactionsVariables): QueryPromise<GetMessageReactionsData, GetMessageReactionsVariables>
export function getMessageReactions(dc: DataConnect, vars: GetMessageReactionsVariables): QueryPromise<GetMessageReactionsData, GetMessageReactionsVariables>

interface GetUserMessageReactionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetUserMessageReactionVariables): QueryRef<GetUserMessageReactionData, GetUserMessageReactionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetUserMessageReactionVariables): QueryRef<GetUserMessageReactionData, GetUserMessageReactionVariables>
  operationName: string
}
export const getUserMessageReactionRef: GetUserMessageReactionRef

export function getUserMessageReaction(vars: GetUserMessageReactionVariables): QueryPromise<GetUserMessageReactionData, GetUserMessageReactionVariables>
export function getUserMessageReaction(dc: DataConnect, vars: GetUserMessageReactionVariables): QueryPromise<GetUserMessageReactionData, GetUserMessageReactionVariables>

interface FindDirectConversationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: FindDirectConversationVariables): QueryRef<FindDirectConversationData, FindDirectConversationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: FindDirectConversationVariables): QueryRef<FindDirectConversationData, FindDirectConversationVariables>
  operationName: string
}
export const findDirectConversationRef: FindDirectConversationRef

export function findDirectConversation(vars: FindDirectConversationVariables): QueryPromise<FindDirectConversationData, FindDirectConversationVariables>
export function findDirectConversation(dc: DataConnect, vars: FindDirectConversationVariables): QueryPromise<FindDirectConversationData, FindDirectConversationVariables>

interface GetAiChatConversationsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetAiChatConversationsVariables): QueryRef<GetAiChatConversationsData, GetAiChatConversationsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetAiChatConversationsVariables): QueryRef<GetAiChatConversationsData, GetAiChatConversationsVariables>
  operationName: string
}
export const getAiChatConversationsRef: GetAiChatConversationsRef

export function getAiChatConversations(vars: GetAiChatConversationsVariables): QueryPromise<GetAiChatConversationsData, GetAiChatConversationsVariables>
export function getAiChatConversations(dc: DataConnect, vars: GetAiChatConversationsVariables): QueryPromise<GetAiChatConversationsData, GetAiChatConversationsVariables>

interface CreateUserRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateUserVariables): MutationRef<CreateUserData, CreateUserVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateUserVariables): MutationRef<CreateUserData, CreateUserVariables>
  operationName: string
}
export const createUserRef: CreateUserRef

export function createUser(vars: CreateUserVariables): MutationPromise<CreateUserData, CreateUserVariables>
export function createUser(dc: DataConnect, vars: CreateUserVariables): MutationPromise<CreateUserData, CreateUserVariables>

interface CreateUserForSignupRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateUserForSignupVariables): MutationRef<CreateUserForSignupData, CreateUserForSignupVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateUserForSignupVariables): MutationRef<CreateUserForSignupData, CreateUserForSignupVariables>
  operationName: string
}
export const createUserForSignupRef: CreateUserForSignupRef

export function createUserForSignup(vars: CreateUserForSignupVariables): MutationPromise<CreateUserForSignupData, CreateUserForSignupVariables>
export function createUserForSignup(dc: DataConnect, vars: CreateUserForSignupVariables): MutationPromise<CreateUserForSignupData, CreateUserForSignupVariables>

interface UpdateUserRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars?: UpdateUserVariables): MutationRef<UpdateUserData, UpdateUserVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars?: UpdateUserVariables): MutationRef<UpdateUserData, UpdateUserVariables>
  operationName: string
}
export const updateUserRef: UpdateUserRef

export function updateUser(vars?: UpdateUserVariables): MutationPromise<UpdateUserData, UpdateUserVariables>
export function updateUser(dc: DataConnect, vars?: UpdateUserVariables): MutationPromise<UpdateUserData, UpdateUserVariables>

interface CreateProfileRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateProfileVariables): MutationRef<CreateProfileData, CreateProfileVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateProfileVariables): MutationRef<CreateProfileData, CreateProfileVariables>
  operationName: string
}
export const createProfileRef: CreateProfileRef

export function createProfile(vars: CreateProfileVariables): MutationPromise<CreateProfileData, CreateProfileVariables>
export function createProfile(dc: DataConnect, vars: CreateProfileVariables): MutationPromise<CreateProfileData, CreateProfileVariables>

interface CreateProfileForSignupRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateProfileForSignupVariables): MutationRef<CreateProfileForSignupData, CreateProfileForSignupVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateProfileForSignupVariables): MutationRef<CreateProfileForSignupData, CreateProfileForSignupVariables>
  operationName: string
}
export const createProfileForSignupRef: CreateProfileForSignupRef

export function createProfileForSignup(vars: CreateProfileForSignupVariables): MutationPromise<CreateProfileForSignupData, CreateProfileForSignupVariables>
export function createProfileForSignup(dc: DataConnect, vars: CreateProfileForSignupVariables): MutationPromise<CreateProfileForSignupData, CreateProfileForSignupVariables>

interface CreateProfileWithBioRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateProfileWithBioVariables): MutationRef<CreateProfileWithBioData, CreateProfileWithBioVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateProfileWithBioVariables): MutationRef<CreateProfileWithBioData, CreateProfileWithBioVariables>
  operationName: string
}
export const createProfileWithBioRef: CreateProfileWithBioRef

export function createProfileWithBio(vars: CreateProfileWithBioVariables): MutationPromise<CreateProfileWithBioData, CreateProfileWithBioVariables>
export function createProfileWithBio(dc: DataConnect, vars: CreateProfileWithBioVariables): MutationPromise<CreateProfileWithBioData, CreateProfileWithBioVariables>

interface UpdateProfileRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateProfileVariables): MutationRef<UpdateProfileData, UpdateProfileVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateProfileVariables): MutationRef<UpdateProfileData, UpdateProfileVariables>
  operationName: string
}
export const updateProfileRef: UpdateProfileRef

export function updateProfile(vars: UpdateProfileVariables): MutationPromise<UpdateProfileData, UpdateProfileVariables>
export function updateProfile(dc: DataConnect, vars: UpdateProfileVariables): MutationPromise<UpdateProfileData, UpdateProfileVariables>

interface UpdateProfileWithBioRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateProfileWithBioVariables): MutationRef<UpdateProfileWithBioData, UpdateProfileWithBioVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateProfileWithBioVariables): MutationRef<UpdateProfileWithBioData, UpdateProfileWithBioVariables>
  operationName: string
}
export const updateProfileWithBioRef: UpdateProfileWithBioRef

export function updateProfileWithBio(vars: UpdateProfileWithBioVariables): MutationPromise<UpdateProfileWithBioData, UpdateProfileWithBioVariables>
export function updateProfileWithBio(dc: DataConnect, vars: UpdateProfileWithBioVariables): MutationPromise<UpdateProfileWithBioData, UpdateProfileWithBioVariables>

interface DeleteProfileRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: DeleteProfileVariables): MutationRef<DeleteProfileData, DeleteProfileVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: DeleteProfileVariables): MutationRef<DeleteProfileData, DeleteProfileVariables>
  operationName: string
}
export const deleteProfileRef: DeleteProfileRef

export function deleteProfile(vars: DeleteProfileVariables): MutationPromise<DeleteProfileData, DeleteProfileVariables>
export function deleteProfile(dc: DataConnect, vars: DeleteProfileVariables): MutationPromise<DeleteProfileData, DeleteProfileVariables>

interface CreateWorkspaceRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateWorkspaceVariables): MutationRef<CreateWorkspaceData, CreateWorkspaceVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateWorkspaceVariables): MutationRef<CreateWorkspaceData, CreateWorkspaceVariables>
  operationName: string
}
export const createWorkspaceRef: CreateWorkspaceRef

export function createWorkspace(vars: CreateWorkspaceVariables): MutationPromise<CreateWorkspaceData, CreateWorkspaceVariables>
export function createWorkspace(dc: DataConnect, vars: CreateWorkspaceVariables): MutationPromise<CreateWorkspaceData, CreateWorkspaceVariables>

interface CreateWorkspaceForSignupRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateWorkspaceForSignupVariables): MutationRef<CreateWorkspaceForSignupData, CreateWorkspaceForSignupVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateWorkspaceForSignupVariables): MutationRef<CreateWorkspaceForSignupData, CreateWorkspaceForSignupVariables>
  operationName: string
}
export const createWorkspaceForSignupRef: CreateWorkspaceForSignupRef

export function createWorkspaceForSignup(vars: CreateWorkspaceForSignupVariables): MutationPromise<CreateWorkspaceForSignupData, CreateWorkspaceForSignupVariables>
export function createWorkspaceForSignup(dc: DataConnect, vars: CreateWorkspaceForSignupVariables): MutationPromise<CreateWorkspaceForSignupData, CreateWorkspaceForSignupVariables>

interface UpdateWorkspaceRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateWorkspaceVariables): MutationRef<UpdateWorkspaceData, UpdateWorkspaceVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateWorkspaceVariables): MutationRef<UpdateWorkspaceData, UpdateWorkspaceVariables>
  operationName: string
}
export const updateWorkspaceRef: UpdateWorkspaceRef

export function updateWorkspace(vars: UpdateWorkspaceVariables): MutationPromise<UpdateWorkspaceData, UpdateWorkspaceVariables>
export function updateWorkspace(dc: DataConnect, vars: UpdateWorkspaceVariables): MutationPromise<UpdateWorkspaceData, UpdateWorkspaceVariables>

interface DeleteWorkspaceRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: DeleteWorkspaceVariables): MutationRef<DeleteWorkspaceData, DeleteWorkspaceVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: DeleteWorkspaceVariables): MutationRef<DeleteWorkspaceData, DeleteWorkspaceVariables>
  operationName: string
}
export const deleteWorkspaceRef: DeleteWorkspaceRef

export function deleteWorkspace(vars: DeleteWorkspaceVariables): MutationPromise<DeleteWorkspaceData, DeleteWorkspaceVariables>
export function deleteWorkspace(dc: DataConnect, vars: DeleteWorkspaceVariables): MutationPromise<DeleteWorkspaceData, DeleteWorkspaceVariables>

interface AddWorkspaceMemberRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: AddWorkspaceMemberVariables): MutationRef<AddWorkspaceMemberData, AddWorkspaceMemberVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: AddWorkspaceMemberVariables): MutationRef<AddWorkspaceMemberData, AddWorkspaceMemberVariables>
  operationName: string
}
export const addWorkspaceMemberRef: AddWorkspaceMemberRef

export function addWorkspaceMember(vars: AddWorkspaceMemberVariables): MutationPromise<AddWorkspaceMemberData, AddWorkspaceMemberVariables>
export function addWorkspaceMember(dc: DataConnect, vars: AddWorkspaceMemberVariables): MutationPromise<AddWorkspaceMemberData, AddWorkspaceMemberVariables>

interface UpdateWorkspaceMemberRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateWorkspaceMemberVariables): MutationRef<UpdateWorkspaceMemberData, UpdateWorkspaceMemberVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateWorkspaceMemberVariables): MutationRef<UpdateWorkspaceMemberData, UpdateWorkspaceMemberVariables>
  operationName: string
}
export const updateWorkspaceMemberRef: UpdateWorkspaceMemberRef

export function updateWorkspaceMember(vars: UpdateWorkspaceMemberVariables): MutationPromise<UpdateWorkspaceMemberData, UpdateWorkspaceMemberVariables>
export function updateWorkspaceMember(dc: DataConnect, vars: UpdateWorkspaceMemberVariables): MutationPromise<UpdateWorkspaceMemberData, UpdateWorkspaceMemberVariables>

interface RemoveWorkspaceMemberRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: RemoveWorkspaceMemberVariables): MutationRef<RemoveWorkspaceMemberData, RemoveWorkspaceMemberVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: RemoveWorkspaceMemberVariables): MutationRef<RemoveWorkspaceMemberData, RemoveWorkspaceMemberVariables>
  operationName: string
}
export const removeWorkspaceMemberRef: RemoveWorkspaceMemberRef

export function removeWorkspaceMember(vars: RemoveWorkspaceMemberVariables): MutationPromise<RemoveWorkspaceMemberData, RemoveWorkspaceMemberVariables>
export function removeWorkspaceMember(dc: DataConnect, vars: RemoveWorkspaceMemberVariables): MutationPromise<RemoveWorkspaceMemberData, RemoveWorkspaceMemberVariables>

interface InviteToWorkspaceRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: InviteToWorkspaceVariables): MutationRef<InviteToWorkspaceData, InviteToWorkspaceVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: InviteToWorkspaceVariables): MutationRef<InviteToWorkspaceData, InviteToWorkspaceVariables>
  operationName: string
}
export const inviteToWorkspaceRef: InviteToWorkspaceRef

export function inviteToWorkspace(vars: InviteToWorkspaceVariables): MutationPromise<InviteToWorkspaceData, InviteToWorkspaceVariables>
export function inviteToWorkspace(dc: DataConnect, vars: InviteToWorkspaceVariables): MutationPromise<InviteToWorkspaceData, InviteToWorkspaceVariables>

interface AcceptInvitationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: AcceptInvitationVariables): MutationRef<AcceptInvitationData, AcceptInvitationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: AcceptInvitationVariables): MutationRef<AcceptInvitationData, AcceptInvitationVariables>
  operationName: string
}
export const acceptInvitationRef: AcceptInvitationRef

export function acceptInvitation(vars: AcceptInvitationVariables): MutationPromise<AcceptInvitationData, AcceptInvitationVariables>
export function acceptInvitation(dc: DataConnect, vars: AcceptInvitationVariables): MutationPromise<AcceptInvitationData, AcceptInvitationVariables>

interface DeclineInvitationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: DeclineInvitationVariables): MutationRef<DeclineInvitationData, DeclineInvitationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: DeclineInvitationVariables): MutationRef<DeclineInvitationData, DeclineInvitationVariables>
  operationName: string
}
export const declineInvitationRef: DeclineInvitationRef

export function declineInvitation(vars: DeclineInvitationVariables): MutationPromise<DeclineInvitationData, DeclineInvitationVariables>
export function declineInvitation(dc: DataConnect, vars: DeclineInvitationVariables): MutationPromise<DeclineInvitationData, DeclineInvitationVariables>

interface CreateBusinessProfileRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateBusinessProfileVariables): MutationRef<CreateBusinessProfileData, CreateBusinessProfileVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateBusinessProfileVariables): MutationRef<CreateBusinessProfileData, CreateBusinessProfileVariables>
  operationName: string
}
export const createBusinessProfileRef: CreateBusinessProfileRef

export function createBusinessProfile(vars: CreateBusinessProfileVariables): MutationPromise<CreateBusinessProfileData, CreateBusinessProfileVariables>
export function createBusinessProfile(dc: DataConnect, vars: CreateBusinessProfileVariables): MutationPromise<CreateBusinessProfileData, CreateBusinessProfileVariables>

interface CreateBusinessProfileWithDescriptionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateBusinessProfileWithDescriptionVariables): MutationRef<CreateBusinessProfileWithDescriptionData, CreateBusinessProfileWithDescriptionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateBusinessProfileWithDescriptionVariables): MutationRef<CreateBusinessProfileWithDescriptionData, CreateBusinessProfileWithDescriptionVariables>
  operationName: string
}
export const createBusinessProfileWithDescriptionRef: CreateBusinessProfileWithDescriptionRef

export function createBusinessProfileWithDescription(vars: CreateBusinessProfileWithDescriptionVariables): MutationPromise<CreateBusinessProfileWithDescriptionData, CreateBusinessProfileWithDescriptionVariables>
export function createBusinessProfileWithDescription(dc: DataConnect, vars: CreateBusinessProfileWithDescriptionVariables): MutationPromise<CreateBusinessProfileWithDescriptionData, CreateBusinessProfileWithDescriptionVariables>

interface UpdateBusinessProfileRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateBusinessProfileVariables): MutationRef<UpdateBusinessProfileData, UpdateBusinessProfileVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateBusinessProfileVariables): MutationRef<UpdateBusinessProfileData, UpdateBusinessProfileVariables>
  operationName: string
}
export const updateBusinessProfileRef: UpdateBusinessProfileRef

export function updateBusinessProfile(vars: UpdateBusinessProfileVariables): MutationPromise<UpdateBusinessProfileData, UpdateBusinessProfileVariables>
export function updateBusinessProfile(dc: DataConnect, vars: UpdateBusinessProfileVariables): MutationPromise<UpdateBusinessProfileData, UpdateBusinessProfileVariables>

interface UpdateBusinessProfileWithDescriptionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateBusinessProfileWithDescriptionVariables): MutationRef<UpdateBusinessProfileWithDescriptionData, UpdateBusinessProfileWithDescriptionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateBusinessProfileWithDescriptionVariables): MutationRef<UpdateBusinessProfileWithDescriptionData, UpdateBusinessProfileWithDescriptionVariables>
  operationName: string
}
export const updateBusinessProfileWithDescriptionRef: UpdateBusinessProfileWithDescriptionRef

export function updateBusinessProfileWithDescription(vars: UpdateBusinessProfileWithDescriptionVariables): MutationPromise<UpdateBusinessProfileWithDescriptionData, UpdateBusinessProfileWithDescriptionVariables>
export function updateBusinessProfileWithDescription(dc: DataConnect, vars: UpdateBusinessProfileWithDescriptionVariables): MutationPromise<UpdateBusinessProfileWithDescriptionData, UpdateBusinessProfileWithDescriptionVariables>

interface CreatePartnerPreferencesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreatePartnerPreferencesVariables): MutationRef<CreatePartnerPreferencesData, CreatePartnerPreferencesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreatePartnerPreferencesVariables): MutationRef<CreatePartnerPreferencesData, CreatePartnerPreferencesVariables>
  operationName: string
}
export const createPartnerPreferencesRef: CreatePartnerPreferencesRef

export function createPartnerPreferences(vars: CreatePartnerPreferencesVariables): MutationPromise<CreatePartnerPreferencesData, CreatePartnerPreferencesVariables>
export function createPartnerPreferences(dc: DataConnect, vars: CreatePartnerPreferencesVariables): MutationPromise<CreatePartnerPreferencesData, CreatePartnerPreferencesVariables>

interface CreatePartnerPreferencesWithEmbeddingRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreatePartnerPreferencesWithEmbeddingVariables): MutationRef<CreatePartnerPreferencesWithEmbeddingData, CreatePartnerPreferencesWithEmbeddingVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreatePartnerPreferencesWithEmbeddingVariables): MutationRef<CreatePartnerPreferencesWithEmbeddingData, CreatePartnerPreferencesWithEmbeddingVariables>
  operationName: string
}
export const createPartnerPreferencesWithEmbeddingRef: CreatePartnerPreferencesWithEmbeddingRef

export function createPartnerPreferencesWithEmbedding(vars: CreatePartnerPreferencesWithEmbeddingVariables): MutationPromise<CreatePartnerPreferencesWithEmbeddingData, CreatePartnerPreferencesWithEmbeddingVariables>
export function createPartnerPreferencesWithEmbedding(dc: DataConnect, vars: CreatePartnerPreferencesWithEmbeddingVariables): MutationPromise<CreatePartnerPreferencesWithEmbeddingData, CreatePartnerPreferencesWithEmbeddingVariables>

interface UpdatePartnerPreferencesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdatePartnerPreferencesVariables): MutationRef<UpdatePartnerPreferencesData, UpdatePartnerPreferencesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdatePartnerPreferencesVariables): MutationRef<UpdatePartnerPreferencesData, UpdatePartnerPreferencesVariables>
  operationName: string
}
export const updatePartnerPreferencesRef: UpdatePartnerPreferencesRef

export function updatePartnerPreferences(vars: UpdatePartnerPreferencesVariables): MutationPromise<UpdatePartnerPreferencesData, UpdatePartnerPreferencesVariables>
export function updatePartnerPreferences(dc: DataConnect, vars: UpdatePartnerPreferencesVariables): MutationPromise<UpdatePartnerPreferencesData, UpdatePartnerPreferencesVariables>

interface UpdatePartnerPreferencesWithEmbeddingRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdatePartnerPreferencesWithEmbeddingVariables): MutationRef<UpdatePartnerPreferencesWithEmbeddingData, UpdatePartnerPreferencesWithEmbeddingVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdatePartnerPreferencesWithEmbeddingVariables): MutationRef<UpdatePartnerPreferencesWithEmbeddingData, UpdatePartnerPreferencesWithEmbeddingVariables>
  operationName: string
}
export const updatePartnerPreferencesWithEmbeddingRef: UpdatePartnerPreferencesWithEmbeddingRef

export function updatePartnerPreferencesWithEmbedding(vars: UpdatePartnerPreferencesWithEmbeddingVariables): MutationPromise<UpdatePartnerPreferencesWithEmbeddingData, UpdatePartnerPreferencesWithEmbeddingVariables>
export function updatePartnerPreferencesWithEmbedding(dc: DataConnect, vars: UpdatePartnerPreferencesWithEmbeddingVariables): MutationPromise<UpdatePartnerPreferencesWithEmbeddingData, UpdatePartnerPreferencesWithEmbeddingVariables>

interface CreateLlmKeyRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateLlmKeyVariables): MutationRef<CreateLlmKeyData, CreateLlmKeyVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateLlmKeyVariables): MutationRef<CreateLlmKeyData, CreateLlmKeyVariables>
  operationName: string
}
export const createLlmKeyRef: CreateLlmKeyRef

export function createLlmKey(vars: CreateLlmKeyVariables): MutationPromise<CreateLlmKeyData, CreateLlmKeyVariables>
export function createLlmKey(dc: DataConnect, vars: CreateLlmKeyVariables): MutationPromise<CreateLlmKeyData, CreateLlmKeyVariables>

interface UpdateLlmKeyRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateLlmKeyVariables): MutationRef<UpdateLlmKeyData, UpdateLlmKeyVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateLlmKeyVariables): MutationRef<UpdateLlmKeyData, UpdateLlmKeyVariables>
  operationName: string
}
export const updateLlmKeyRef: UpdateLlmKeyRef

export function updateLlmKey(vars: UpdateLlmKeyVariables): MutationPromise<UpdateLlmKeyData, UpdateLlmKeyVariables>
export function updateLlmKey(dc: DataConnect, vars: UpdateLlmKeyVariables): MutationPromise<UpdateLlmKeyData, UpdateLlmKeyVariables>

interface DeleteLlmKeyRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: DeleteLlmKeyVariables): MutationRef<DeleteLlmKeyData, DeleteLlmKeyVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: DeleteLlmKeyVariables): MutationRef<DeleteLlmKeyData, DeleteLlmKeyVariables>
  operationName: string
}
export const deleteLlmKeyRef: DeleteLlmKeyRef

export function deleteLlmKey(vars: DeleteLlmKeyVariables): MutationPromise<DeleteLlmKeyData, DeleteLlmKeyVariables>
export function deleteLlmKey(dc: DataConnect, vars: DeleteLlmKeyVariables): MutationPromise<DeleteLlmKeyData, DeleteLlmKeyVariables>

interface CreateAuditLogRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateAuditLogVariables): MutationRef<CreateAuditLogData, CreateAuditLogVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateAuditLogVariables): MutationRef<CreateAuditLogData, CreateAuditLogVariables>
  operationName: string
}
export const createAuditLogRef: CreateAuditLogRef

export function createAuditLog(vars: CreateAuditLogVariables): MutationPromise<CreateAuditLogData, CreateAuditLogVariables>
export function createAuditLog(dc: DataConnect, vars: CreateAuditLogVariables): MutationPromise<CreateAuditLogData, CreateAuditLogVariables>

interface CreateUserSessionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateUserSessionVariables): MutationRef<CreateUserSessionData, CreateUserSessionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateUserSessionVariables): MutationRef<CreateUserSessionData, CreateUserSessionVariables>
  operationName: string
}
export const createUserSessionRef: CreateUserSessionRef

export function createUserSession(vars: CreateUserSessionVariables): MutationPromise<CreateUserSessionData, CreateUserSessionVariables>
export function createUserSession(dc: DataConnect, vars: CreateUserSessionVariables): MutationPromise<CreateUserSessionData, CreateUserSessionVariables>

interface UpdateUserSessionActivityRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateUserSessionActivityVariables): MutationRef<UpdateUserSessionActivityData, UpdateUserSessionActivityVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateUserSessionActivityVariables): MutationRef<UpdateUserSessionActivityData, UpdateUserSessionActivityVariables>
  operationName: string
}
export const updateUserSessionActivityRef: UpdateUserSessionActivityRef

export function updateUserSessionActivity(vars: UpdateUserSessionActivityVariables): MutationPromise<UpdateUserSessionActivityData, UpdateUserSessionActivityVariables>
export function updateUserSessionActivity(dc: DataConnect, vars: UpdateUserSessionActivityVariables): MutationPromise<UpdateUserSessionActivityData, UpdateUserSessionActivityVariables>

interface RevokeUserSessionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: RevokeUserSessionVariables): MutationRef<RevokeUserSessionData, RevokeUserSessionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: RevokeUserSessionVariables): MutationRef<RevokeUserSessionData, RevokeUserSessionVariables>
  operationName: string
}
export const revokeUserSessionRef: RevokeUserSessionRef

export function revokeUserSession(vars: RevokeUserSessionVariables): MutationPromise<RevokeUserSessionData, RevokeUserSessionVariables>
export function revokeUserSession(dc: DataConnect, vars: RevokeUserSessionVariables): MutationPromise<RevokeUserSessionData, RevokeUserSessionVariables>

interface DeleteUserSessionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: DeleteUserSessionVariables): MutationRef<DeleteUserSessionData, DeleteUserSessionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: DeleteUserSessionVariables): MutationRef<DeleteUserSessionData, DeleteUserSessionVariables>
  operationName: string
}
export const deleteUserSessionRef: DeleteUserSessionRef

export function deleteUserSession(vars: DeleteUserSessionVariables): MutationPromise<DeleteUserSessionData, DeleteUserSessionVariables>
export function deleteUserSession(dc: DataConnect, vars: DeleteUserSessionVariables): MutationPromise<DeleteUserSessionData, DeleteUserSessionVariables>

interface CreateAnalyticsEventRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateAnalyticsEventVariables): MutationRef<CreateAnalyticsEventData, CreateAnalyticsEventVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateAnalyticsEventVariables): MutationRef<CreateAnalyticsEventData, CreateAnalyticsEventVariables>
  operationName: string
}
export const createAnalyticsEventRef: CreateAnalyticsEventRef

export function createAnalyticsEvent(vars: CreateAnalyticsEventVariables): MutationPromise<CreateAnalyticsEventData, CreateAnalyticsEventVariables>
export function createAnalyticsEvent(dc: DataConnect, vars: CreateAnalyticsEventVariables): MutationPromise<CreateAnalyticsEventData, CreateAnalyticsEventVariables>

interface CreateEmbeddingRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateEmbeddingVariables): MutationRef<CreateEmbeddingData, CreateEmbeddingVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateEmbeddingVariables): MutationRef<CreateEmbeddingData, CreateEmbeddingVariables>
  operationName: string
}
export const createEmbeddingRef: CreateEmbeddingRef

export function createEmbedding(vars: CreateEmbeddingVariables): MutationPromise<CreateEmbeddingData, CreateEmbeddingVariables>
export function createEmbedding(dc: DataConnect, vars: CreateEmbeddingVariables): MutationPromise<CreateEmbeddingData, CreateEmbeddingVariables>

interface UpdateEmbeddingRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: UpdateEmbeddingVariables): MutationRef<UpdateEmbeddingData, UpdateEmbeddingVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: UpdateEmbeddingVariables): MutationRef<UpdateEmbeddingData, UpdateEmbeddingVariables>
  operationName: string
}
export const updateEmbeddingRef: UpdateEmbeddingRef

export function updateEmbedding(vars: UpdateEmbeddingVariables): MutationPromise<UpdateEmbeddingData, UpdateEmbeddingVariables>
export function updateEmbedding(dc: DataConnect, vars: UpdateEmbeddingVariables): MutationPromise<UpdateEmbeddingData, UpdateEmbeddingVariables>

interface SoftDeleteEmbeddingRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: SoftDeleteEmbeddingVariables): MutationRef<SoftDeleteEmbeddingData, SoftDeleteEmbeddingVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: SoftDeleteEmbeddingVariables): MutationRef<SoftDeleteEmbeddingData, SoftDeleteEmbeddingVariables>
  operationName: string
}
export const softDeleteEmbeddingRef: SoftDeleteEmbeddingRef

export function softDeleteEmbedding(vars: SoftDeleteEmbeddingVariables): MutationPromise<SoftDeleteEmbeddingData, SoftDeleteEmbeddingVariables>
export function softDeleteEmbedding(dc: DataConnect, vars: SoftDeleteEmbeddingVariables): MutationPromise<SoftDeleteEmbeddingData, SoftDeleteEmbeddingVariables>

interface DeleteEmbeddingRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: DeleteEmbeddingVariables): MutationRef<DeleteEmbeddingData, DeleteEmbeddingVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: DeleteEmbeddingVariables): MutationRef<DeleteEmbeddingData, DeleteEmbeddingVariables>
  operationName: string
}
export const deleteEmbeddingRef: DeleteEmbeddingRef

export function deleteEmbedding(vars: DeleteEmbeddingVariables): MutationPromise<DeleteEmbeddingData, DeleteEmbeddingVariables>
export function deleteEmbedding(dc: DataConnect, vars: DeleteEmbeddingVariables): MutationPromise<DeleteEmbeddingData, DeleteEmbeddingVariables>

interface CreateEmbeddingBatchRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateEmbeddingBatchVariables): MutationRef<CreateEmbeddingBatchData, CreateEmbeddingBatchVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateEmbeddingBatchVariables): MutationRef<CreateEmbeddingBatchData, CreateEmbeddingBatchVariables>
  operationName: string
}
export const createEmbeddingBatchRef: CreateEmbeddingBatchRef

export function createEmbeddingBatch(vars: CreateEmbeddingBatchVariables): MutationPromise<CreateEmbeddingBatchData, CreateEmbeddingBatchVariables>
export function createEmbeddingBatch(dc: DataConnect, vars: CreateEmbeddingBatchVariables): MutationPromise<CreateEmbeddingBatchData, CreateEmbeddingBatchVariables>

interface GetAllUsersRef {
  /* Allow users to create refs without passing in DataConnect */
  (): QueryRef<GetAllUsersData, undefined>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect): QueryRef<GetAllUsersData, undefined>
  operationName: string
}
export const getAllUsersRef: GetAllUsersRef

export function getAllUsers(): QueryPromise<GetAllUsersData, undefined>
export function getAllUsers(dc: DataConnect): QueryPromise<GetAllUsersData, undefined>

interface GetCurrentUserRef {
  /* Allow users to create refs without passing in DataConnect */
  (): QueryRef<GetCurrentUserData, undefined>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect): QueryRef<GetCurrentUserData, undefined>
  operationName: string
}
export const getCurrentUserRef: GetCurrentUserRef

export function getCurrentUser(): QueryPromise<GetCurrentUserData, undefined>
export function getCurrentUser(dc: DataConnect): QueryPromise<GetCurrentUserData, undefined>

interface SearchProfilesByBioRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: SearchProfilesByBioVariables): QueryRef<SearchProfilesByBioData, SearchProfilesByBioVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: SearchProfilesByBioVariables): QueryRef<SearchProfilesByBioData, SearchProfilesByBioVariables>
  operationName: string
}
export const searchProfilesByBioRef: SearchProfilesByBioRef

export function searchProfilesByBio(vars: SearchProfilesByBioVariables): QueryPromise<SearchProfilesByBioData, SearchProfilesByBioVariables>
export function searchProfilesByBio(dc: DataConnect, vars: SearchProfilesByBioVariables): QueryPromise<SearchProfilesByBioData, SearchProfilesByBioVariables>

interface SearchBusinessProfilesByDescriptionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: SearchBusinessProfilesByDescriptionVariables): QueryRef<SearchBusinessProfilesByDescriptionData, SearchBusinessProfilesByDescriptionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: SearchBusinessProfilesByDescriptionVariables): QueryRef<SearchBusinessProfilesByDescriptionData, SearchBusinessProfilesByDescriptionVariables>
  operationName: string
}
export const searchBusinessProfilesByDescriptionRef: SearchBusinessProfilesByDescriptionRef

export function searchBusinessProfilesByDescription(vars: SearchBusinessProfilesByDescriptionVariables): QueryPromise<SearchBusinessProfilesByDescriptionData, SearchBusinessProfilesByDescriptionVariables>
export function searchBusinessProfilesByDescription(dc: DataConnect, vars: SearchBusinessProfilesByDescriptionVariables): QueryPromise<SearchBusinessProfilesByDescriptionData, SearchBusinessProfilesByDescriptionVariables>

interface MatchProfileToBusinessesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: MatchProfileToBusinessesVariables): QueryRef<MatchProfileToBusinessesData, MatchProfileToBusinessesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: MatchProfileToBusinessesVariables): QueryRef<MatchProfileToBusinessesData, MatchProfileToBusinessesVariables>
  operationName: string
}
export const matchProfileToBusinessesRef: MatchProfileToBusinessesRef

export function matchProfileToBusinesses(vars: MatchProfileToBusinessesVariables): QueryPromise<MatchProfileToBusinessesData, MatchProfileToBusinessesVariables>
export function matchProfileToBusinesses(dc: DataConnect, vars: MatchProfileToBusinessesVariables): QueryPromise<MatchProfileToBusinessesData, MatchProfileToBusinessesVariables>

interface BusinessProfilesBySimilarityRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: BusinessProfilesBySimilarityVariables): QueryRef<BusinessProfilesBySimilarityData, BusinessProfilesBySimilarityVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: BusinessProfilesBySimilarityVariables): QueryRef<BusinessProfilesBySimilarityData, BusinessProfilesBySimilarityVariables>
  operationName: string
}
export const businessProfilesBySimilarityRef: BusinessProfilesBySimilarityRef

export function businessProfilesBySimilarity(vars: BusinessProfilesBySimilarityVariables): QueryPromise<BusinessProfilesBySimilarityData, BusinessProfilesBySimilarityVariables>
export function businessProfilesBySimilarity(dc: DataConnect, vars: BusinessProfilesBySimilarityVariables): QueryPromise<BusinessProfilesBySimilarityData, BusinessProfilesBySimilarityVariables>

interface MatchBusinessToProfilesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: MatchBusinessToProfilesVariables): QueryRef<MatchBusinessToProfilesData, MatchBusinessToProfilesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: MatchBusinessToProfilesVariables): QueryRef<MatchBusinessToProfilesData, MatchBusinessToProfilesVariables>
  operationName: string
}
export const matchBusinessToProfilesRef: MatchBusinessToProfilesRef

export function matchBusinessToProfiles(vars: MatchBusinessToProfilesVariables): QueryPromise<MatchBusinessToProfilesData, MatchBusinessToProfilesVariables>
export function matchBusinessToProfiles(dc: DataConnect, vars: MatchBusinessToProfilesVariables): QueryPromise<MatchBusinessToProfilesData, MatchBusinessToProfilesVariables>

interface ProfilesBySimilarityRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: ProfilesBySimilarityVariables): QueryRef<ProfilesBySimilarityData, ProfilesBySimilarityVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: ProfilesBySimilarityVariables): QueryRef<ProfilesBySimilarityData, ProfilesBySimilarityVariables>
  operationName: string
}
export const profilesBySimilarityRef: ProfilesBySimilarityRef

export function profilesBySimilarity(vars: ProfilesBySimilarityVariables): QueryPromise<ProfilesBySimilarityData, ProfilesBySimilarityVariables>
export function profilesBySimilarity(dc: DataConnect, vars: ProfilesBySimilarityVariables): QueryPromise<ProfilesBySimilarityData, ProfilesBySimilarityVariables>

interface SearchPartnerPreferencesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: SearchPartnerPreferencesVariables): QueryRef<SearchPartnerPreferencesData, SearchPartnerPreferencesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: SearchPartnerPreferencesVariables): QueryRef<SearchPartnerPreferencesData, SearchPartnerPreferencesVariables>
  operationName: string
}
export const searchPartnerPreferencesRef: SearchPartnerPreferencesRef

export function searchPartnerPreferences(vars: SearchPartnerPreferencesVariables): QueryPromise<SearchPartnerPreferencesData, SearchPartnerPreferencesVariables>
export function searchPartnerPreferences(dc: DataConnect, vars: SearchPartnerPreferencesVariables): QueryPromise<SearchPartnerPreferencesData, SearchPartnerPreferencesVariables>

interface GetUserRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetUserVariables): QueryRef<GetUserData, GetUserVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetUserVariables): QueryRef<GetUserData, GetUserVariables>
  operationName: string
}
export const getUserRef: GetUserRef

export function getUser(vars: GetUserVariables): QueryPromise<GetUserData, GetUserVariables>
export function getUser(dc: DataConnect, vars: GetUserVariables): QueryPromise<GetUserData, GetUserVariables>

interface GetUserProfilesRef {
  /* Allow users to create refs without passing in DataConnect */
  (): QueryRef<GetUserProfilesData, undefined>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect): QueryRef<GetUserProfilesData, undefined>
  operationName: string
}
export const getUserProfilesRef: GetUserProfilesRef

export function getUserProfiles(): QueryPromise<GetUserProfilesData, undefined>
export function getUserProfiles(dc: DataConnect): QueryPromise<GetUserProfilesData, undefined>

interface GetUserWorkspacesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetUserWorkspacesVariables): QueryRef<GetUserWorkspacesData, GetUserWorkspacesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetUserWorkspacesVariables): QueryRef<GetUserWorkspacesData, GetUserWorkspacesVariables>
  operationName: string
}
export const getUserWorkspacesRef: GetUserWorkspacesRef

export function getUserWorkspaces(vars: GetUserWorkspacesVariables): QueryPromise<GetUserWorkspacesData, GetUserWorkspacesVariables>
export function getUserWorkspaces(dc: DataConnect, vars: GetUserWorkspacesVariables): QueryPromise<GetUserWorkspacesData, GetUserWorkspacesVariables>

interface GetWorkspaceRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetWorkspaceVariables): QueryRef<GetWorkspaceData, GetWorkspaceVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetWorkspaceVariables): QueryRef<GetWorkspaceData, GetWorkspaceVariables>
  operationName: string
}
export const getWorkspaceRef: GetWorkspaceRef

export function getWorkspace(vars: GetWorkspaceVariables): QueryPromise<GetWorkspaceData, GetWorkspaceVariables>
export function getWorkspace(dc: DataConnect, vars: GetWorkspaceVariables): QueryPromise<GetWorkspaceData, GetWorkspaceVariables>

interface GetWorkspaceMembersRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetWorkspaceMembersVariables): QueryRef<GetWorkspaceMembersData, GetWorkspaceMembersVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetWorkspaceMembersVariables): QueryRef<GetWorkspaceMembersData, GetWorkspaceMembersVariables>
  operationName: string
}
export const getWorkspaceMembersRef: GetWorkspaceMembersRef

export function getWorkspaceMembers(vars: GetWorkspaceMembersVariables): QueryPromise<GetWorkspaceMembersData, GetWorkspaceMembersVariables>
export function getWorkspaceMembers(dc: DataConnect, vars: GetWorkspaceMembersVariables): QueryPromise<GetWorkspaceMembersData, GetWorkspaceMembersVariables>

interface GetBusinessProfileRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetBusinessProfileVariables): QueryRef<GetBusinessProfileData, GetBusinessProfileVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetBusinessProfileVariables): QueryRef<GetBusinessProfileData, GetBusinessProfileVariables>
  operationName: string
}
export const getBusinessProfileRef: GetBusinessProfileRef

export function getBusinessProfile(vars: GetBusinessProfileVariables): QueryPromise<GetBusinessProfileData, GetBusinessProfileVariables>
export function getBusinessProfile(dc: DataConnect, vars: GetBusinessProfileVariables): QueryPromise<GetBusinessProfileData, GetBusinessProfileVariables>

interface GetPartnerPreferencesRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetPartnerPreferencesVariables): QueryRef<GetPartnerPreferencesData, GetPartnerPreferencesVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetPartnerPreferencesVariables): QueryRef<GetPartnerPreferencesData, GetPartnerPreferencesVariables>
  operationName: string
}
export const getPartnerPreferencesRef: GetPartnerPreferencesRef

export function getPartnerPreferences(vars: GetPartnerPreferencesVariables): QueryPromise<GetPartnerPreferencesData, GetPartnerPreferencesVariables>
export function getPartnerPreferences(dc: DataConnect, vars: GetPartnerPreferencesVariables): QueryPromise<GetPartnerPreferencesData, GetPartnerPreferencesVariables>

interface GetWorkspaceInvitationsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetWorkspaceInvitationsVariables): QueryRef<GetWorkspaceInvitationsData, GetWorkspaceInvitationsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetWorkspaceInvitationsVariables): QueryRef<GetWorkspaceInvitationsData, GetWorkspaceInvitationsVariables>
  operationName: string
}
export const getWorkspaceInvitationsRef: GetWorkspaceInvitationsRef

export function getWorkspaceInvitations(vars: GetWorkspaceInvitationsVariables): QueryPromise<GetWorkspaceInvitationsData, GetWorkspaceInvitationsVariables>
export function getWorkspaceInvitations(dc: DataConnect, vars: GetWorkspaceInvitationsVariables): QueryPromise<GetWorkspaceInvitationsData, GetWorkspaceInvitationsVariables>

interface GetPendingInvitationsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetPendingInvitationsVariables): QueryRef<GetPendingInvitationsData, GetPendingInvitationsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetPendingInvitationsVariables): QueryRef<GetPendingInvitationsData, GetPendingInvitationsVariables>
  operationName: string
}
export const getPendingInvitationsRef: GetPendingInvitationsRef

export function getPendingInvitations(vars: GetPendingInvitationsVariables): QueryPromise<GetPendingInvitationsData, GetPendingInvitationsVariables>
export function getPendingInvitations(dc: DataConnect, vars: GetPendingInvitationsVariables): QueryPromise<GetPendingInvitationsData, GetPendingInvitationsVariables>

interface GetLlmKeysByEntityRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetLlmKeysByEntityVariables): QueryRef<GetLlmKeysByEntityData, GetLlmKeysByEntityVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetLlmKeysByEntityVariables): QueryRef<GetLlmKeysByEntityData, GetLlmKeysByEntityVariables>
  operationName: string
}
export const getLlmKeysByEntityRef: GetLlmKeysByEntityRef

export function getLlmKeysByEntity(vars: GetLlmKeysByEntityVariables): QueryPromise<GetLlmKeysByEntityData, GetLlmKeysByEntityVariables>
export function getLlmKeysByEntity(dc: DataConnect, vars: GetLlmKeysByEntityVariables): QueryPromise<GetLlmKeysByEntityData, GetLlmKeysByEntityVariables>

interface GetLlmKeyByProviderAndEntityRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetLlmKeyByProviderAndEntityVariables): QueryRef<GetLlmKeyByProviderAndEntityData, GetLlmKeyByProviderAndEntityVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetLlmKeyByProviderAndEntityVariables): QueryRef<GetLlmKeyByProviderAndEntityData, GetLlmKeyByProviderAndEntityVariables>
  operationName: string
}
export const getLlmKeyByProviderAndEntityRef: GetLlmKeyByProviderAndEntityRef

export function getLlmKeyByProviderAndEntity(vars: GetLlmKeyByProviderAndEntityVariables): QueryPromise<GetLlmKeyByProviderAndEntityData, GetLlmKeyByProviderAndEntityVariables>
export function getLlmKeyByProviderAndEntity(dc: DataConnect, vars: GetLlmKeyByProviderAndEntityVariables): QueryPromise<GetLlmKeyByProviderAndEntityData, GetLlmKeyByProviderAndEntityVariables>

interface GetWorkspaceLlmKeyRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetWorkspaceLlmKeyVariables): QueryRef<GetWorkspaceLlmKeyData, GetWorkspaceLlmKeyVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetWorkspaceLlmKeyVariables): QueryRef<GetWorkspaceLlmKeyData, GetWorkspaceLlmKeyVariables>
  operationName: string
}
export const getWorkspaceLlmKeyRef: GetWorkspaceLlmKeyRef

export function getWorkspaceLlmKey(vars: GetWorkspaceLlmKeyVariables): QueryPromise<GetWorkspaceLlmKeyData, GetWorkspaceLlmKeyVariables>
export function getWorkspaceLlmKey(dc: DataConnect, vars: GetWorkspaceLlmKeyVariables): QueryPromise<GetWorkspaceLlmKeyData, GetWorkspaceLlmKeyVariables>

interface GetUserLlmKeyRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetUserLlmKeyVariables): QueryRef<GetUserLlmKeyData, GetUserLlmKeyVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetUserLlmKeyVariables): QueryRef<GetUserLlmKeyData, GetUserLlmKeyVariables>
  operationName: string
}
export const getUserLlmKeyRef: GetUserLlmKeyRef

export function getUserLlmKey(vars: GetUserLlmKeyVariables): QueryPromise<GetUserLlmKeyData, GetUserLlmKeyVariables>
export function getUserLlmKey(dc: DataConnect, vars: GetUserLlmKeyVariables): QueryPromise<GetUserLlmKeyData, GetUserLlmKeyVariables>

interface GetProfileLlmKeysRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetProfileLlmKeysVariables): QueryRef<GetProfileLlmKeysData, GetProfileLlmKeysVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetProfileLlmKeysVariables): QueryRef<GetProfileLlmKeysData, GetProfileLlmKeysVariables>
  operationName: string
}
export const getProfileLlmKeysRef: GetProfileLlmKeysRef

export function getProfileLlmKeys(vars: GetProfileLlmKeysVariables): QueryPromise<GetProfileLlmKeysData, GetProfileLlmKeysVariables>
export function getProfileLlmKeys(dc: DataConnect, vars: GetProfileLlmKeysVariables): QueryPromise<GetProfileLlmKeysData, GetProfileLlmKeysVariables>

interface GetAuditLogsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars?: GetAuditLogsVariables): QueryRef<GetAuditLogsData, GetAuditLogsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars?: GetAuditLogsVariables): QueryRef<GetAuditLogsData, GetAuditLogsVariables>
  operationName: string
}
export const getAuditLogsRef: GetAuditLogsRef

export function getAuditLogs(vars?: GetAuditLogsVariables): QueryPromise<GetAuditLogsData, GetAuditLogsVariables>
export function getAuditLogs(dc: DataConnect, vars?: GetAuditLogsVariables): QueryPromise<GetAuditLogsData, GetAuditLogsVariables>

interface GetAuditLogsByUserRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetAuditLogsByUserVariables): QueryRef<GetAuditLogsByUserData, GetAuditLogsByUserVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetAuditLogsByUserVariables): QueryRef<GetAuditLogsByUserData, GetAuditLogsByUserVariables>
  operationName: string
}
export const getAuditLogsByUserRef: GetAuditLogsByUserRef

export function getAuditLogsByUser(vars: GetAuditLogsByUserVariables): QueryPromise<GetAuditLogsByUserData, GetAuditLogsByUserVariables>
export function getAuditLogsByUser(dc: DataConnect, vars: GetAuditLogsByUserVariables): QueryPromise<GetAuditLogsByUserData, GetAuditLogsByUserVariables>

interface GetAuditLogsByWorkspaceRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetAuditLogsByWorkspaceVariables): QueryRef<GetAuditLogsByWorkspaceData, GetAuditLogsByWorkspaceVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetAuditLogsByWorkspaceVariables): QueryRef<GetAuditLogsByWorkspaceData, GetAuditLogsByWorkspaceVariables>
  operationName: string
}
export const getAuditLogsByWorkspaceRef: GetAuditLogsByWorkspaceRef

export function getAuditLogsByWorkspace(vars: GetAuditLogsByWorkspaceVariables): QueryPromise<GetAuditLogsByWorkspaceData, GetAuditLogsByWorkspaceVariables>
export function getAuditLogsByWorkspace(dc: DataConnect, vars: GetAuditLogsByWorkspaceVariables): QueryPromise<GetAuditLogsByWorkspaceData, GetAuditLogsByWorkspaceVariables>

interface GetAuditLogsBySeverityRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetAuditLogsBySeverityVariables): QueryRef<GetAuditLogsBySeverityData, GetAuditLogsBySeverityVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetAuditLogsBySeverityVariables): QueryRef<GetAuditLogsBySeverityData, GetAuditLogsBySeverityVariables>
  operationName: string
}
export const getAuditLogsBySeverityRef: GetAuditLogsBySeverityRef

export function getAuditLogsBySeverity(vars: GetAuditLogsBySeverityVariables): QueryPromise<GetAuditLogsBySeverityData, GetAuditLogsBySeverityVariables>
export function getAuditLogsBySeverity(dc: DataConnect, vars: GetAuditLogsBySeverityVariables): QueryPromise<GetAuditLogsBySeverityData, GetAuditLogsBySeverityVariables>

interface GetActiveSessionsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetActiveSessionsVariables): QueryRef<GetActiveSessionsData, GetActiveSessionsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetActiveSessionsVariables): QueryRef<GetActiveSessionsData, GetActiveSessionsVariables>
  operationName: string
}
export const getActiveSessionsRef: GetActiveSessionsRef

export function getActiveSessions(vars: GetActiveSessionsVariables): QueryPromise<GetActiveSessionsData, GetActiveSessionsVariables>
export function getActiveSessions(dc: DataConnect, vars: GetActiveSessionsVariables): QueryPromise<GetActiveSessionsData, GetActiveSessionsVariables>

interface GetSessionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetSessionVariables): QueryRef<GetSessionData, GetSessionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetSessionVariables): QueryRef<GetSessionData, GetSessionVariables>
  operationName: string
}
export const getSessionRef: GetSessionRef

export function getSession(vars: GetSessionVariables): QueryPromise<GetSessionData, GetSessionVariables>
export function getSession(dc: DataConnect, vars: GetSessionVariables): QueryPromise<GetSessionData, GetSessionVariables>

interface GetUserSessionsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetUserSessionsVariables): QueryRef<GetUserSessionsData, GetUserSessionsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetUserSessionsVariables): QueryRef<GetUserSessionsData, GetUserSessionsVariables>
  operationName: string
}
export const getUserSessionsRef: GetUserSessionsRef

export function getUserSessions(vars: GetUserSessionsVariables): QueryPromise<GetUserSessionsData, GetUserSessionsVariables>
export function getUserSessions(dc: DataConnect, vars: GetUserSessionsVariables): QueryPromise<GetUserSessionsData, GetUserSessionsVariables>

interface GetEmbeddingsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetEmbeddingsVariables): QueryRef<GetEmbeddingsData, GetEmbeddingsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetEmbeddingsVariables): QueryRef<GetEmbeddingsData, GetEmbeddingsVariables>
  operationName: string
}
export const getEmbeddingsRef: GetEmbeddingsRef

export function getEmbeddings(vars: GetEmbeddingsVariables): QueryPromise<GetEmbeddingsData, GetEmbeddingsVariables>
export function getEmbeddings(dc: DataConnect, vars: GetEmbeddingsVariables): QueryPromise<GetEmbeddingsData, GetEmbeddingsVariables>

interface GetEmbeddingByEntityRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetEmbeddingByEntityVariables): QueryRef<GetEmbeddingByEntityData, GetEmbeddingByEntityVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetEmbeddingByEntityVariables): QueryRef<GetEmbeddingByEntityData, GetEmbeddingByEntityVariables>
  operationName: string
}
export const getEmbeddingByEntityRef: GetEmbeddingByEntityRef

export function getEmbeddingByEntity(vars: GetEmbeddingByEntityVariables): QueryPromise<GetEmbeddingByEntityData, GetEmbeddingByEntityVariables>
export function getEmbeddingByEntity(dc: DataConnect, vars: GetEmbeddingByEntityVariables): QueryPromise<GetEmbeddingByEntityData, GetEmbeddingByEntityVariables>

interface SearchEmbeddingsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: SearchEmbeddingsVariables): QueryRef<SearchEmbeddingsData, SearchEmbeddingsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: SearchEmbeddingsVariables): QueryRef<SearchEmbeddingsData, SearchEmbeddingsVariables>
  operationName: string
}
export const searchEmbeddingsRef: SearchEmbeddingsRef

export function searchEmbeddings(vars: SearchEmbeddingsVariables): QueryPromise<SearchEmbeddingsData, SearchEmbeddingsVariables>
export function searchEmbeddings(dc: DataConnect, vars: SearchEmbeddingsVariables): QueryPromise<SearchEmbeddingsData, SearchEmbeddingsVariables>

interface CreateUploadRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateUploadVariables): MutationRef<CreateUploadData, CreateUploadVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateUploadVariables): MutationRef<CreateUploadData, CreateUploadVariables>
  operationName: string
}
export const createUploadRef: CreateUploadRef

export function createUpload(vars: CreateUploadVariables): MutationPromise<CreateUploadData, CreateUploadVariables>
export function createUpload(dc: DataConnect, vars: CreateUploadVariables): MutationPromise<CreateUploadData, CreateUploadVariables>

interface LinkUserPhotoUploadRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LinkUserPhotoUploadVariables): MutationRef<LinkUserPhotoUploadData, LinkUserPhotoUploadVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LinkUserPhotoUploadVariables): MutationRef<LinkUserPhotoUploadData, LinkUserPhotoUploadVariables>
  operationName: string
}
export const linkUserPhotoUploadRef: LinkUserPhotoUploadRef

export function linkUserPhotoUpload(vars: LinkUserPhotoUploadVariables): MutationPromise<LinkUserPhotoUploadData, LinkUserPhotoUploadVariables>
export function linkUserPhotoUpload(dc: DataConnect, vars: LinkUserPhotoUploadVariables): MutationPromise<LinkUserPhotoUploadData, LinkUserPhotoUploadVariables>

interface ListUploadsByWorkspaceRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: ListUploadsByWorkspaceVariables): QueryRef<ListUploadsByWorkspaceData, ListUploadsByWorkspaceVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: ListUploadsByWorkspaceVariables): QueryRef<ListUploadsByWorkspaceData, ListUploadsByWorkspaceVariables>
  operationName: string
}
export const listUploadsByWorkspaceRef: ListUploadsByWorkspaceRef

export function listUploadsByWorkspace(vars: ListUploadsByWorkspaceVariables): QueryPromise<ListUploadsByWorkspaceData, ListUploadsByWorkspaceVariables>
export function listUploadsByWorkspace(dc: DataConnect, vars: ListUploadsByWorkspaceVariables): QueryPromise<ListUploadsByWorkspaceData, ListUploadsByWorkspaceVariables>

interface GetUploadRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetUploadVariables): QueryRef<GetUploadData, GetUploadVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetUploadVariables): QueryRef<GetUploadData, GetUploadVariables>
  operationName: string
}
export const getUploadRef: GetUploadRef

export function getUpload(vars: GetUploadVariables): QueryPromise<GetUploadData, GetUploadVariables>
export function getUpload(dc: DataConnect, vars: GetUploadVariables): QueryPromise<GetUploadData, GetUploadVariables>

interface LogSecurityEventRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LogSecurityEventVariables): MutationRef<LogSecurityEventData, LogSecurityEventVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LogSecurityEventVariables): MutationRef<LogSecurityEventData, LogSecurityEventVariables>
  operationName: string
}
export const logSecurityEventRef: LogSecurityEventRef

export function logSecurityEvent(vars: LogSecurityEventVariables): MutationPromise<LogSecurityEventData, LogSecurityEventVariables>
export function logSecurityEvent(dc: DataConnect, vars: LogSecurityEventVariables): MutationPromise<LogSecurityEventData, LogSecurityEventVariables>

interface LogAuthenticationEventRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LogAuthenticationEventVariables): MutationRef<LogAuthenticationEventData, LogAuthenticationEventVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LogAuthenticationEventVariables): MutationRef<LogAuthenticationEventData, LogAuthenticationEventVariables>
  operationName: string
}
export const logAuthenticationEventRef: LogAuthenticationEventRef

export function logAuthenticationEvent(vars: LogAuthenticationEventVariables): MutationPromise<LogAuthenticationEventData, LogAuthenticationEventVariables>
export function logAuthenticationEvent(dc: DataConnect, vars: LogAuthenticationEventVariables): MutationPromise<LogAuthenticationEventData, LogAuthenticationEventVariables>

interface LogFailedAuthenticationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LogFailedAuthenticationVariables): MutationRef<LogFailedAuthenticationData, LogFailedAuthenticationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LogFailedAuthenticationVariables): MutationRef<LogFailedAuthenticationData, LogFailedAuthenticationVariables>
  operationName: string
}
export const logFailedAuthenticationRef: LogFailedAuthenticationRef

export function logFailedAuthentication(vars: LogFailedAuthenticationVariables): MutationPromise<LogFailedAuthenticationData, LogFailedAuthenticationVariables>
export function logFailedAuthentication(dc: DataConnect, vars: LogFailedAuthenticationVariables): MutationPromise<LogFailedAuthenticationData, LogFailedAuthenticationVariables>

interface LogDataAccessRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LogDataAccessVariables): MutationRef<LogDataAccessData, LogDataAccessVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LogDataAccessVariables): MutationRef<LogDataAccessData, LogDataAccessVariables>
  operationName: string
}
export const logDataAccessRef: LogDataAccessRef

export function logDataAccess(vars: LogDataAccessVariables): MutationPromise<LogDataAccessData, LogDataAccessVariables>
export function logDataAccess(dc: DataConnect, vars: LogDataAccessVariables): MutationPromise<LogDataAccessData, LogDataAccessVariables>

interface LogPermissionChangeRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LogPermissionChangeVariables): MutationRef<LogPermissionChangeData, LogPermissionChangeVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LogPermissionChangeVariables): MutationRef<LogPermissionChangeData, LogPermissionChangeVariables>
  operationName: string
}
export const logPermissionChangeRef: LogPermissionChangeRef

export function logPermissionChange(vars: LogPermissionChangeVariables): MutationPromise<LogPermissionChangeData, LogPermissionChangeVariables>
export function logPermissionChange(dc: DataConnect, vars: LogPermissionChangeVariables): MutationPromise<LogPermissionChangeData, LogPermissionChangeVariables>

interface CreateSecureSessionRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: CreateSecureSessionVariables): MutationRef<CreateSecureSessionData, CreateSecureSessionVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: CreateSecureSessionVariables): MutationRef<CreateSecureSessionData, CreateSecureSessionVariables>
  operationName: string
}
export const createSecureSessionRef: CreateSecureSessionRef

export function createSecureSession(vars: CreateSecureSessionVariables): MutationPromise<CreateSecureSessionData, CreateSecureSessionVariables>
export function createSecureSession(dc: DataConnect, vars: CreateSecureSessionVariables): MutationPromise<CreateSecureSessionData, CreateSecureSessionVariables>

interface RevokeSessionWithAuditRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: RevokeSessionWithAuditVariables): MutationRef<RevokeSessionWithAuditData, RevokeSessionWithAuditVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: RevokeSessionWithAuditVariables): MutationRef<RevokeSessionWithAuditData, RevokeSessionWithAuditVariables>
  operationName: string
}
export const revokeSessionWithAuditRef: RevokeSessionWithAuditRef

export function revokeSessionWithAudit(vars: RevokeSessionWithAuditVariables): MutationPromise<RevokeSessionWithAuditData, RevokeSessionWithAuditVariables>
export function revokeSessionWithAudit(dc: DataConnect, vars: RevokeSessionWithAuditVariables): MutationPromise<RevokeSessionWithAuditData, RevokeSessionWithAuditVariables>

interface LogBatchDataOperationRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LogBatchDataOperationVariables): MutationRef<LogBatchDataOperationData, LogBatchDataOperationVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LogBatchDataOperationVariables): MutationRef<LogBatchDataOperationData, LogBatchDataOperationVariables>
  operationName: string
}
export const logBatchDataOperationRef: LogBatchDataOperationRef

export function logBatchDataOperation(vars: LogBatchDataOperationVariables): MutationPromise<LogBatchDataOperationData, LogBatchDataOperationVariables>
export function logBatchDataOperation(dc: DataConnect, vars: LogBatchDataOperationVariables): MutationPromise<LogBatchDataOperationData, LogBatchDataOperationVariables>

interface LogWorkspaceEventRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: LogWorkspaceEventVariables): MutationRef<LogWorkspaceEventData, LogWorkspaceEventVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: LogWorkspaceEventVariables): MutationRef<LogWorkspaceEventData, LogWorkspaceEventVariables>
  operationName: string
}
export const logWorkspaceEventRef: LogWorkspaceEventRef

export function logWorkspaceEvent(vars: LogWorkspaceEventVariables): MutationPromise<LogWorkspaceEventData, LogWorkspaceEventVariables>
export function logWorkspaceEvent(dc: DataConnect, vars: LogWorkspaceEventVariables): MutationPromise<LogWorkspaceEventData, LogWorkspaceEventVariables>

interface EmergencyRevokeAllUserSessionsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: EmergencyRevokeAllUserSessionsVariables): MutationRef<EmergencyRevokeAllUserSessionsData, EmergencyRevokeAllUserSessionsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: EmergencyRevokeAllUserSessionsVariables): MutationRef<EmergencyRevokeAllUserSessionsData, EmergencyRevokeAllUserSessionsVariables>
  operationName: string
}
export const emergencyRevokeAllUserSessionsRef: EmergencyRevokeAllUserSessionsRef

export function emergencyRevokeAllUserSessions(vars: EmergencyRevokeAllUserSessionsVariables): MutationPromise<EmergencyRevokeAllUserSessionsData, EmergencyRevokeAllUserSessionsVariables>
export function emergencyRevokeAllUserSessions(dc: DataConnect, vars: EmergencyRevokeAllUserSessionsVariables): MutationPromise<EmergencyRevokeAllUserSessionsData, EmergencyRevokeAllUserSessionsVariables>

interface SearchAuditLogsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars?: SearchAuditLogsVariables): QueryRef<SearchAuditLogsData, SearchAuditLogsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars?: SearchAuditLogsVariables): QueryRef<SearchAuditLogsData, SearchAuditLogsVariables>
  operationName: string
}
export const searchAuditLogsRef: SearchAuditLogsRef

export function searchAuditLogs(vars?: SearchAuditLogsVariables): QueryPromise<SearchAuditLogsData, SearchAuditLogsVariables>
export function searchAuditLogs(dc: DataConnect, vars?: SearchAuditLogsVariables): QueryPromise<SearchAuditLogsData, SearchAuditLogsVariables>

interface GetFailedLoginAttemptsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars?: GetFailedLoginAttemptsVariables): QueryRef<GetFailedLoginAttemptsData, GetFailedLoginAttemptsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars?: GetFailedLoginAttemptsVariables): QueryRef<GetFailedLoginAttemptsData, GetFailedLoginAttemptsVariables>
  operationName: string
}
export const getFailedLoginAttemptsRef: GetFailedLoginAttemptsRef

export function getFailedLoginAttempts(vars?: GetFailedLoginAttemptsVariables): QueryPromise<GetFailedLoginAttemptsData, GetFailedLoginAttemptsVariables>
export function getFailedLoginAttempts(dc: DataConnect, vars?: GetFailedLoginAttemptsVariables): QueryPromise<GetFailedLoginAttemptsData, GetFailedLoginAttemptsVariables>

interface GetSuspiciousActivityRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetSuspiciousActivityVariables): QueryRef<GetSuspiciousActivityData, GetSuspiciousActivityVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetSuspiciousActivityVariables): QueryRef<GetSuspiciousActivityData, GetSuspiciousActivityVariables>
  operationName: string
}
export const getSuspiciousActivityRef: GetSuspiciousActivityRef

export function getSuspiciousActivity(vars: GetSuspiciousActivityVariables): QueryPromise<GetSuspiciousActivityData, GetSuspiciousActivityVariables>
export function getSuspiciousActivity(dc: DataConnect, vars: GetSuspiciousActivityVariables): QueryPromise<GetSuspiciousActivityData, GetSuspiciousActivityVariables>

interface GetActiveSessionsForUserRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetActiveSessionsForUserVariables): QueryRef<GetActiveSessionsForUserData, GetActiveSessionsForUserVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetActiveSessionsForUserVariables): QueryRef<GetActiveSessionsForUserData, GetActiveSessionsForUserVariables>
  operationName: string
}
export const getActiveSessionsForUserRef: GetActiveSessionsForUserRef

export function getActiveSessionsForUser(vars: GetActiveSessionsForUserVariables): QueryPromise<GetActiveSessionsForUserData, GetActiveSessionsForUserVariables>
export function getActiveSessionsForUser(dc: DataConnect, vars: GetActiveSessionsForUserVariables): QueryPromise<GetActiveSessionsForUserData, GetActiveSessionsForUserVariables>

interface GetConcurrentSessionsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetConcurrentSessionsVariables): QueryRef<GetConcurrentSessionsData, GetConcurrentSessionsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetConcurrentSessionsVariables): QueryRef<GetConcurrentSessionsData, GetConcurrentSessionsVariables>
  operationName: string
}
export const getConcurrentSessionsRef: GetConcurrentSessionsRef

export function getConcurrentSessions(vars: GetConcurrentSessionsVariables): QueryPromise<GetConcurrentSessionsData, GetConcurrentSessionsVariables>
export function getConcurrentSessions(dc: DataConnect, vars: GetConcurrentSessionsVariables): QueryPromise<GetConcurrentSessionsData, GetConcurrentSessionsVariables>

interface GetSessionsByIpRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetSessionsByIpVariables): QueryRef<GetSessionsByIpData, GetSessionsByIpVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetSessionsByIpVariables): QueryRef<GetSessionsByIpData, GetSessionsByIpVariables>
  operationName: string
}
export const getSessionsByIpRef: GetSessionsByIpRef

export function getSessionsByIp(vars: GetSessionsByIpVariables): QueryPromise<GetSessionsByIpData, GetSessionsByIpVariables>
export function getSessionsByIp(dc: DataConnect, vars: GetSessionsByIpVariables): QueryPromise<GetSessionsByIpData, GetSessionsByIpVariables>

interface GetWorkspaceSecurityEventsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetWorkspaceSecurityEventsVariables): QueryRef<GetWorkspaceSecurityEventsData, GetWorkspaceSecurityEventsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetWorkspaceSecurityEventsVariables): QueryRef<GetWorkspaceSecurityEventsData, GetWorkspaceSecurityEventsVariables>
  operationName: string
}
export const getWorkspaceSecurityEventsRef: GetWorkspaceSecurityEventsRef

export function getWorkspaceSecurityEvents(vars: GetWorkspaceSecurityEventsVariables): QueryPromise<GetWorkspaceSecurityEventsData, GetWorkspaceSecurityEventsVariables>
export function getWorkspaceSecurityEvents(dc: DataConnect, vars: GetWorkspaceSecurityEventsVariables): QueryPromise<GetWorkspaceSecurityEventsData, GetWorkspaceSecurityEventsVariables>

interface GetDataAccessLogsRef {
  /* Allow users to create refs without passing in DataConnect */
  (vars: GetDataAccessLogsVariables): QueryRef<GetDataAccessLogsData, GetDataAccessLogsVariables>
  /* Allow users to pass in custom DataConnect instances */
  (dc: DataConnect, vars: GetDataAccessLogsVariables): QueryRef<GetDataAccessLogsData, GetDataAccessLogsVariables>
  operationName: string
}
export const getDataAccessLogsRef: GetDataAccessLogsRef

export function getDataAccessLogs(vars: GetDataAccessLogsVariables): QueryPromise<GetDataAccessLogsData, GetDataAccessLogsVariables>
export function getDataAccessLogs(dc: DataConnect, vars: GetDataAccessLogsVariables): QueryPromise<GetDataAccessLogsData, GetDataAccessLogsVariables>
