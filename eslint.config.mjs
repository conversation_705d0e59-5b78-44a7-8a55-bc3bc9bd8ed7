import antfu from '@antfu/eslint-config'

export default antfu({
  typescript: true,
  vue: true,
  stylistic: true,
  formatters: {
    css: true,
    html: true,
  },
  // ESLint 9+ uses flat config. Use `ignores` here instead of a .eslintignore file.
  ignores: [
    'db/**',
    'documentation/**',
    '.app/public/**',
    'functions/lib/**',
  ],
  rules: {
    'unused-imports/no-unused-vars': 'off',
    'ts/ban-ts-comment': 'off',
    // Temporary workaround: unicorn rule crashes on ESLint 9.x in current plugin version
    'unicorn/error-message': 'off',
  },
})
