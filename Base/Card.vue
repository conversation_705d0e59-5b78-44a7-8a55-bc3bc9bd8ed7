<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    /**
     * The radius of the card.
     *
     * @since 2.0.0
     * @default 'sm'
     */
    rounded?: 'none' | 'sm' | 'md' | 'lg'
    item_id?: string

    /**
     * The color of the card.
     *
     * @default 'default'
     */
    color?:
      | 'default'
      | 'default-contrast'
      | 'muted'
      | 'muted-contrast'
      | 'dark'
      | 'black'
      | 'primary'
      | 'info'
      | 'success'
      | 'warning'
      | 'danger'
      | 'none'

    /**
     * Adds a flat or a on hover shadow to the card.
     */
    shadow?: 'flat' | 'hover'
  }>(),
  {
    rounded: undefined,
    shadow: undefined,
    color: undefined,
    item_id: Math.random().toString(36).substring(7),
  },
)

const rounded = useNuiDefaultProperty(props, 'BaseCard', 'rounded')
const color = useNuiDefaultProperty(props, 'BaseCard', 'color')

const radiuses = {
  none: '',
  sm: 'nui-card-rounded-sm',
  md: 'nui-card-rounded-md',
  lg: 'nui-card-rounded-lg',
} as Record<string, string>

const colors = {
  'default': 'nui-card-default',
  'default-contrast': 'nui-card-default-contrast',
  'muted': 'nui-card-muted',
  'muted-contrast': 'nui-card-muted-contrast',
  'dark': 'nui-card-dark',
  'black': 'nui-card-black',
  'primary': 'nui-card-primary',
  'info': 'nui-card-info',
  'success': 'nui-card-success',
  'warning': 'nui-card-warning',
  'danger': 'nui-card-danger',
  'none': '',
} as Record<string, string>

const shadows = {
  flat: 'nui-card-shadow',
  hover: 'nui-card-shadow-hover',
}

const classes = computed(() => [
  'nui-card',
  rounded.value && radiuses[rounded.value],
  color.value && colors[color.value],
  props.shadow && shadows[props.shadow],
])
</script>

<template>
  <div :class="classes">
    <slot />
  </div>
</template>
