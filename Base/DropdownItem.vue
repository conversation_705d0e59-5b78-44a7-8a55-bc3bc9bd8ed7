<script setup lang="ts">
import type { RouteLocationRaw } from 'vue-router'
import { MenuItem } from '@headlessui/vue'

const props = withDefaults(
  defineProps<{
    /**
     * The route to navigate to when the button is clicked.
     */
    to?: RouteLocationRaw

    /** Using href instead of to result in a native anchor with no router functionality. */
    href?: string

    /**
     * The value for the `rel` attribute on the button.
     */
    rel?: string
    item_id?: string
    /**
     * The value for the `target` attribute on the button.
     */
    target?: string

    /**
     * The type of button.
     */
    type?: 'button' | 'submit' | 'reset'

    /**
     * The radius of the dropdown-item.
     *
     * @since 2.0.0
     * @default 'sm'
     */
    rounded?: 'none' | 'sm' | 'md' | 'lg'

    /**
     * The contrast of the dropdown-item.
     *
     * @default 'default'
     */
    contrast?: 'default' | 'contrast'

    /**
     * The hover color of the dropdown-item inner elements.
     *
     * @since 3.0.0
     * @default 'primary'
     */
    color?:
      | 'primary'
      | 'info'
      | 'success'
      | 'warning'
      | 'danger'
      | 'dark'
      | 'black'

    /**
     * The title to display for the dropdown item.
     */
    title?: string

    /**
     * The text to display for the dropdown item.
     */
    text?: string

    /**
     * Optional CSS classes to apply to the wrapper and inner elements.
     */
    classes?: {
      /**
       * CSS classes to apply to the wrapper element.
       */
      wrapper?: string | string[]

      /**
       * CSS classes to apply to the title element.
       */
      title?: string | string[]

      /**
       * CSS classes to apply to the text element.
       */
      text?: string | string[]
    }

    /**
     * Whether the button is disabled.
     */
    disabled?: boolean
  }>(),
  {
    to: undefined,
    href: undefined,
    rel: undefined,
    target: undefined,
    type: undefined,
    rounded: undefined,
    contrast: undefined,
    color: undefined,
    title: undefined,
    text: undefined,
    classes: () => ({
      title:
        'font-heading text-muted-800 text-xs font-semibold leading-tight dark:text-white',
      text: 'text-muted-400 font-sans text-xs',
    }),
    item_id: Math.random().toString(36).substring(7),
  },
)

const rounded = useNuiDefaultProperty(props, 'BaseDropdownItem', 'rounded')
const contrast = useNuiDefaultProperty(props, 'BaseDropdownItem', 'contrast')
const color = useNuiDefaultProperty(props, 'BaseDropdownItem', 'color')

const radiuses = {
  none: '',
  sm: 'nui-item-rounded-sm',
  md: 'nui-item-rounded-md',
  lg: 'nui-item-rounded-lg',
} as Record<string, string>

const contrasts = {
  default: 'nui-item-default',
  contrast: 'nui-item-contrast',
} as Record<string, string>

const colors = {
  primary: 'nui-item-primary',
  info: 'nui-item-info',
  success: 'nui-item-success',
  warning: 'nui-item-warning',
  danger: 'nui-item-danger',
  dark: 'nui-item-dark',
  black: 'nui-item-black',
} as Record<string, string>

const { is, attributes } = useNinjaButton(props)
</script>

<template>
  <client-only>
    <MenuItem
      v-slot="{ active, close }: { active: boolean; close: () => void }"
      as="div"
    >
      <component
        :is="is"
        v-bind="attributes"
        class="nui-dropdown-item"
        :class="[
          active && 'nui-active',
          rounded && radiuses[rounded],
          contrast && contrasts[contrast],
          color && colors[color],
        ]"
        @click.passive="close"
      >
        <slot name="start" />
        <div class="nui-item-content">
          <div :class="props.classes?.title">
            <slot>{{ props.title }}</slot>
          </div>
          <p
            v-if="'text' in $slots || props.text"
            class="text-muted-400 font-sans text-xs"
            :class="props.classes?.text"
          >
            <slot name="text">
              {{ props.text }}
            </slot>
          </p>
        </div>
        <slot name="end" />
      </component>
    </MenuItem>
  </client-only>
</template>
