<script setup lang="ts">
defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(
  defineProps<{
    /**
     * The form input identifier.
     */
    id?: string
    item_id?: string
    /**
     * The type of input.
     */
    type?: string

    /**
     * The radius of the input.
     *
     * @since 2.0.0
     * @default 'rounded'
     */
    rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'

    /**
     * The size of the input.
     *
     * @default 'md'
     */
    size?: 'sm' | 'md' | 'lg'

    /**
     * The contrast of the input.
     *
     * @default 'default'
     */
    contrast?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast'

    /**
     * The label to display for the input.
     */
    label?: string

    /**
     * If the label should be floating.
     */
    labelFloat?: boolean

    /**
     * The icon to display for the input.
     */
    icon?: string

    /**
     * The placeholder to display for the input.
     */
    placeholder?: string

    /**
     * An error message or boolean value indicating whether the input is in an error state.
     */
    error?: string | boolean

    /**
     * Whether the color of the input should change when it is focused.
     */
    colorFocus?: boolean

    /**
     * Whether the input is in a loading state.
     */
    loading?: boolean
    multi?: boolean

    /**
     * Optional CSS classes to apply to the wrapper, label, input, addon, error, and icon elements.
     */
    classes?: {
      /**
       * CSS classes to apply to the wrapper element.
       */
      wrapper?: string | string[]

      /**
       * CSS classes to apply to the outer element.
       */
      outer?: string | string[]

      /**
       * CSS classes to apply to the label element.
       */
      label?: string | string[]

      /**
       * CSS classes to apply to the input element.
       */
      input?: string | string[]

      /**
       * CSS classes to apply to the addon element.
       */
      addon?: string | string[]

      /**
       * CSS classes to apply to the error element.
       */
      error?: string | string[]

      /**
       * CSS classes to apply to the icon element.
       */
      icon?: string | string[]
    }
  }>(),
  {
    id: undefined,
    type: 'text',
    rounded: undefined,
    size: undefined,
    contrast: undefined,
    label: undefined,
    icon: undefined,
    placeholder: undefined,
    error: false,
    multi: false,
    classes: () => ({}),
    item_id: Math.random().toString(36).substring(7),
  },
)

const [modelValue, modelModifiers] = defineModel<any, 'lazy'>({
  set(value) {
    return value
  },
})

const rounded = useNuiDefaultProperty(props, 'BaseInput', 'rounded')
const size = useNuiDefaultProperty(props, 'BaseInput', 'size')
const contrast = useNuiDefaultProperty(props, 'BaseInput', 'contrast')

const inputRef = ref<HTMLInputElement>()
const id = useNinjaId(() => props.id)

const radiuses = {
  none: '',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  full: 'rounded-full',
} as Record<string, string>

const sizes = {
  sm: 'nui-input-sm',
  md: 'nui-input-md',
  lg: 'nui-input-lg',
} as Record<string, string>

const contrasts = {
  'default': 'nui-input-default',
  'default-contrast': 'nui-input-default-contrast',
  'muted': 'nui-input-muted',
  'muted-contrast': 'nui-input-muted-contrast',
} as Record<string, string>

defineExpose({
  /**
   * The underlying HTMLInputElement element.
   */
  el: inputRef,

  /**
   * The internal id of the radio input.
   */
  id,
})

const placeholder = computed(() => {
  if (props.loading) {
    return
  }
  if (props.labelFloat) {
    return props.label
  }

  return props.placeholder
})

if (process.dev) {
  const slots = useSlots()
  if (props.labelFloat && 'label' in slots) {
    console.warn(
      '[ninja-ui][base-input] The "label-float" property is not compatible with the label slot, use the label property instead.',
    )
  }
}

const isOpen = ref(false)

const showFileManager = ref(false)
const emitFile: any = getCurrentInstance()
const images = ref([])
const image = ref({ src: '' })

function uploadAdd(data: any) {
  console.log(data)
  if (!data)
    return
  // remove duplicates
  data = data.filter((v: any, i: any, a: any) => a.indexOf(v) === i)

  data = data.map((item: any) => {
    return {
      uid: item.uid,
      id: item.id,
      src: item.src,
      name: item.name,
      type: item.type,
      size: item.size,
    }
  })
  if (props.multi) {
    modelValue.value = data
    images.value = data
  }
  else {
    modelValue.value = data[data.length - 1]
    image.value = data[data.length - 1]
  }

  showFileManager.value = false
}
</script>

<template>
  <div class="w-full">
    <label class="nui-input-label mb-4">
      {{ label }}
    </label>
    <button
      type="button"
      class="border-muted-200 dark:border-muted-700 hover:ring-muted-200 dark:hover:ring-muted-700 dark:bg-muted-800 dark:ring-offset-muted-900 flex size-9 items-center justify-center rounded-full border bg-white ring-1 ring-transparent transition-all duration-300 hover:ring-offset-4"
      @click="showFileManager = !showFileManager"
    >
      <Icon
        name="ic:baseline-add-photo-alternate"
        class="text-muted-400 size-5"
      />
    </button>
    <div v-if="multi" class="grid gap-4 md:grid-cols-3">
      <div
        v-for="(photo, index) in images"
        :key="index"
        class="group relative mb-4 before:absolute before:inset-0 before:rounded-md before:bg-black before:bg-opacity-20 before:content-['']"
      >
        <img
          class="w-full rounded-md bg-blue-500 transition delay-150 duration-300 ease-in-out hover:-translate-y-1 hover:scale-150 hover:bg-indigo-500"
          :src="photo.src"
        >
      </div>
    </div>
    <div v-else class="grid gap-4 md:grid-cols-3">
      <div
        class="group relative mb-4 before:absolute before:inset-0 before:rounded-md before:bg-black before:bg-opacity-20 before:content-['']"
      >
        <img
          class="w-full rounded-md bg-blue-500 transition delay-150 duration-300 ease-in-out hover:-translate-y-1 hover:scale-150 hover:bg-indigo-500"
          :src="image.src"
        >
      </div>
    </div>

    <div
      v-show="showFileManager"
      tabindex="0"
      class="fixed inset-0 z-50 size-full overflow-auto py-6"
    >
      <div
        v-show="showFileManager"
        class="relative z-50 mx-auto my-0 w-3/4 max-w-full p-3"
      >
        <div
          class="flex flex-col overflow-hidden rounded border bg-white shadow-lg dark:border-gray-700 dark:bg-gray-800"
        >
          <button
            class="font-3xl absolute right-0 top-0 m-6 size-6 fill-current font-bold rtl:left-0"
            @click="showFileManager = false"
          >
            &times;
          </button>
          <!-- modal title -->
          <div
            class="border-b px-6 py-3 text-xl font-bold dark:border-gray-700"
          >
            Upload Icon
          </div>
          <!-- modal content -->
          <div class="grow p-6">
            <FileManager @input="uploadAdd" />
          </div>
          <div class="flex justify-end border-t px-6 py-3 dark:border-gray-700">
            <button
              type="button"
              class="o_btn_neutral"
              @click="showFileManager = false"
            >
              Close
            </button>
            <button
              type="button"
              class="bg-primary_focus border-primary_focus hover:bg-primary hover:border-primary focus:bg-primary focus:border-primary inline-block rounded border px-4 py-2 text-center leading-5 text-gray-100 hover:text-white hover:ring-0 focus:outline-none focus:ring-0"
            >
              Saves Changes
            </button>
          </div>
        </div>
      </div>
      <div
        class="fixed inset-0 z-40 size-full overflow-auto bg-black opacity-50"
      />
    </div>
  </div>
</template>
