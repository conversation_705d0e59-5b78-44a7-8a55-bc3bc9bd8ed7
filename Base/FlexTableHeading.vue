<script setup lang="ts">
const props: any = withDefaults(
  defineProps<{
    /**
     * The type of the heading.
     */
    type?: 'grow' | 'shrink' | 'stable'
    item_id?: string
  }>(),
  {
    type: 'stable',
    item_id: Math.random().toString(36).substring(7),
  },
)
</script>

<template>
  <div
    class="text-muted-400 p-4 font-sans text-xs font-medium uppercase"
    :class="[
      props.type === 'grow' && 'md:grow',
      props.type === 'shrink' && 'md:shrink',
      props.type === 'stable'
        && 'sm:w-[90px] md:line-clamp-1 md:w-[110px] md:shrink-0',
    ]"
  >
    <slot />
  </div>
</template>
