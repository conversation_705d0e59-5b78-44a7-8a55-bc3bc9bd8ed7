<script setup lang="ts">
const prop = defineProps({
  industry: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: 'Select an Industry',
  },
  item_id: {
    type: String,
    default: Math.random().toString(36).substring(7),
  },
})
const filter = ref('')
const value = ref('')
const perPage = ref(40)
const page = ref(1)

onMounted(() => {
  if (prop.industry)
    value.value = prop.industry
})

watch(
  () => prop.industry,
  () => {
    if (prop.industry)
      value.value = prop.industry
  },
)

const industries = ref([
  {
    name: 'Industry 1',
  },
  {
    name: 'Industry 2',
  },
  {
    name: 'Industry 3',
  },
  {
    name: 'Industry 4',
  },
  {
    name: 'Industry 5',
  },
])

const em: any = getCurrentInstance()
watch(
  () => value.value,
  () => {
    em.emit('industrySelect', value.value)
  },
)
</script>

<template>
  <div>
    <BaseSelect
      v-model="value"
      shape="rounded"
      :label="label"
    >
      <option
        v-for="(ind, index) in industries"
        :key="index"
        :value="ind.name"
      >
        {{ ind.name }}
      </option>
    </BaseSelect>
  </div>
</template>
