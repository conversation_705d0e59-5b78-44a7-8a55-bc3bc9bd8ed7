<script setup lang='ts'>
const props = withDefaults(
  defineProps<{
    item_id?: string
  }>(),
  {

    item_id: Math.random().toString(36).substring(7),
  },
)
</script>

<template>
  <div class="relative">
    <div class="hidden justify-between md:flex md:items-center">
      <slot name="header" />
    </div>

    <div class="flex flex-col gap-y-4">
      <slot />
    </div>
  </div>
</template>
