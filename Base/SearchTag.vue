<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    shape?: 'straight' | 'rounded' | 'curved'
    tags?: string[]
    collection?: string
    keys?: string
    /**
     * The form input identifier.
     */
    id?: string
    item_id?: string
    /**
     * The type of input.
     */
    type?: string

    /**
     * The radius of the input.
     *
     * @since 2.0.0
     * @default 'rounded'
     */
    rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'

    /**
     * The size of the input.
     *
     * @default 'md'
     */
    size?: 'sm' | 'md' | 'lg'

    /**
     * The contrast of the input.
     *
     * @default 'default'
     */
    contrast?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast'

    /**
     * The label to display for the input.
     */
    label?: string

    /**
     * If the label should be floating.
     */
    labelFloat?: boolean

    /**
     * The icon to display for the input.
     */
    icon?: string

    /**
     * The placeholder to display for the input.
     */
    placeholder?: string

    /**
     * An error message or boolean value indicating whether the input is in an error state.
     */
    error?: string | boolean

    /**
     * Whether the color of the input should change when it is focused.
     */
    colorFocus?: boolean

    /**
     * Whether the input is in a loading state.
     */
    loading?: boolean

    /**
     * Optional CSS classes to apply to the wrapper, label, input, addon, error, and icon elements.
     */
    classes?: {
      /**
       * CSS classes to apply to the wrapper element.
       */
      wrapper?: string | string[]

      /**
       * CSS classes to apply to the outer element.
       */
      outer?: string | string[]

      /**
       * CSS classes to apply to the label element.
       */
      label?: string | string[]

      /**
       * CSS classes to apply to the input element.
       */
      input?: string | string[]

      /**
       * CSS classes to apply to the addon element.
       */
      addon?: string | string[]

      /**
       * CSS classes to apply to the error element.
       */
      error?: string | string[]

      /**
       * CSS classes to apply to the icon element.
       */
      icon?: string | string[]
    }
  }>(),
  {
    shape: 'rounded',
    tags: [],
    collection: 'tags',
    keys: 'name',
    id: undefined,
    type: 'text',
    rounded: undefined,
    size: undefined,
    contrast: undefined,
    label: undefined,
    icon: undefined,
    placeholder: undefined,
    error: false,
    classes: () => ({}),
    item_id: Math.random().toString(36).substring(7),
  },
)

const [modelValue, modelModifiers] = defineModel<
  string | number,
  'lazy' | 'trim' | 'number'
>({
  set(value) {
    if (modelModifiers.number) {
      return looseToNumber(value)
    }
    else if (modelModifiers.trim && typeof value === 'string') {
      return value.trim()
    }

    return value
  },
})
const items = ref([])
const search = ref('')
const selected = ref([])
function addItem(tag) {
  // check if array

  if (modelValue.value.includes(tag[props.keys])) {
    modelValue.value = modelValue.value.filter(item => item !== tag[props.keys])
  }
  else {
    modelValue.value = [...modelValue.value, tag[props.keys]]
  }
}

onMounted(() => {
})

const rounded = useNuiDefaultProperty(props, 'BaseInput', 'rounded')
const size = useNuiDefaultProperty(props, 'BaseInput', 'size')
const contrast = useNuiDefaultProperty(props, 'BaseInput', 'contrast')

const inputRef = ref<HTMLInputElement>()
const id = useNinjaId(() => props.id)

const radiuses = {
  none: '',
  sm: 'nui-input-rounded-sm',
  md: 'nui-input-rounded-md',
  lg: 'nui-input-rounded-lg',
  full: 'nui-input-rounded-full',
} as Record<string, string>

const sizes = {
  sm: 'nui-input-sm',
  md: 'nui-input-md',
  lg: 'nui-input-lg',
} as Record<string, string>

const contrasts = {
  'default': 'nui-input-default',
  'default-contrast': 'nui-input-default-contrast',
  'muted': 'nui-input-muted',
  'muted-contrast': 'nui-input-muted-contrast',
} as Record<string, string>

defineExpose({
  /**
   * The underlying HTMLInputElement element.
   */
  el: inputRef,

  /**
   * The internal id of the radio input.
   */
  id,
})

const placeholder = computed(() => {
  if (props.loading) {
    return
  }
  if (props.labelFloat) {
    return props.label
  }

  return props.placeholder
})

if (process.dev) {
  const slots = useSlots()
  if (props.labelFloat && 'label' in slots) {
    console.warn(
      '[ninja-ui][base-input] The "label-float" property is not compatible with the label slot, use the label property instead.',
    )
  }
}
</script>

<template>
  <div

    class="nui-input-wrapper w-full"
    :class="[
      contrast && contrasts[contrast],
      size && sizes[size],
      rounded && radiuses[rounded],
      props.error && !props.loading && 'nui-input-error',
      props.loading && 'nui-input-loading',
      props.labelFloat && 'nui-input-label-float',
      props.icon && 'nui-has-icon',
      props.colorFocus && 'nui-input-focus',
      props.classes?.wrapper,
    ]"
  >
    <label
      v-if="
        ('label' in $slots && !props.labelFloat)
          || (props.label && !props.labelFloat)
      "
      class="nui-input-label"
      :for="id"
      :class="props.classes?.label"
    >
      <slot name="label">{{ props.label }}</slot>
    </label>
    <div class="relative flex w-full items-center gap-2">
      <BaseInput
        v-model="search"
        icon="lucide:search"
        :placeholder="`Add a ${collection}`"
        :shape="props.shape"
        :classes="{
          wrapper: 'w-full',
          input:
            'pe-24 ',
        }"
        class="nui-input w-full"
        :class="props.classes?.input"
      />
      <div
        type="button"
        class="hover:text-primary-500 dark:text-muted-400 bg-muted-200 dark:bg-muted-700/40 absolute end-1 top-1 inline-flex size-8 items-center justify-center rounded-full font-sans text-sm text-white hover:border"
      >
        <Icon name="lucide:plus" class="nui-input-icon-inner" />
      </div>
    </div>
    <div class="mt-2 flex flex-wrap gap-2">
      <span
        v-for="(tag, index) in items"
        :key="index"
        class="text-muted-500 dark:text-muted-400 bg-muted-200 dark:bg-muted-700/40 inline-flex h-6 items-center justify-center rounded-full px-3 font-sans text-xs font-medium"
        :class="{
          'cursor-pointer border ': !modelValue.includes(tag[props.keys]),
          'border-primary-600 cursor-pointer border': modelValue.includes(tag[props.keys]),
        }"
        @click="addItem(tag)"
      >
        {{ tag[keys] }}
      </span>
    </div>
  </div>
</template>: { [x: string]: string; }(: any)(: any): { [x: string]: string; }(: any)(: any)
