<script setup lang="ts">
import { useColorMode } from '@vueuse/core'

const props = withDefaults(
  defineProps<{
    /**
     * Disables transitions when toggling between light and dark mode.
     */
    disableTransitions?: boolean
    item_id?: string
    value?: boolean
  }>(),
  {
    disableTransitions: undefined,
    item_id: Math.random().toString(36).substring(7),
  },
)
const disableTransitions = useNuiDefaultProperty(
  props,
  'BaseThemeSwitch',
  'disableTransitions',
)
onMounted(() => {
  if (props.value) {
    modelValue.value = props.value
  }
})
const colorMode = useColorMode()
const isDark = computed({
  get() {
    return colorMode.value === 'dark'
  },
  set(value) {
    // disable transitions
    if (process.client && disableTransitions.value) {
      document.documentElement.classList.add('nui-no-transition')
    }

    colorMode.preference = value ? 'dark' : 'light'

    // re-enable transitions
    if (process.client && disableTransitions.value) {
      setTimeout(() => {
        document.documentElement.classList.remove('nui-no-transition')
      }, 0)
    }
  },
})
</script>

<template>
  <label class="nui-theme-switch">
    <input
      v-model="isDark"
      class="nui-theme-switch-input"
      type="checkbox"
    >
    <span class="nui-theme-switch-inner">
      <IconSun class="nui-sun" />
      <IconMoon class="nui-moon" />
    </span>
  </label>
</template>

<style>
.nui-no-transition * {
  transition-property: none !important;
  transition-duration: 0 !important;
}
</style>
